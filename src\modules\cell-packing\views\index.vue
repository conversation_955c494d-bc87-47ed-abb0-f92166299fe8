<template>
  <hips-view
    :style="{ background: '#eee' }"
    :header-height="44"
    :footer-height="62"
    safe-area
  >
    <hips-nav-bar
      slot="header"
      :height="44"
      :back-button="{
        showLeftArrow: true,
      }"
      :title="pageText('page.title', '电芯装箱')"
      @nav-bar-back-click="exit"
    >
      <span
        slot="right"
        class="nav-bar-right"
        @click="$refs.deviceLoginRef.open()"
      >
        设备登录
      </span>
    </hips-nav-bar>
    <div class="pda-view-content pda-view-content-edit">
      <!-- 工单 -->
      <div class="content-card">
        <div
          class="pda-input-row pda-input-row-edit"
          style="align-items: center"
        >
          <div class="pda-input-row-icon">
            <img
              src="@/assets/img/add-container-o.svg"
              class="label-icon"
              alt="icon"
            />
          </div>
          <div class="pda-input-row-input">
            <hips-input
              v-model="workOrderNum"
              readonly
              :placeholder="pageText('workOrderNum', '请选择工单')"
              type="text"
              input-align="center"
              @click.native="handleOpenWorkOrder"
            />
          </div>
          <div class="pda-input-row-icon">
            <div
              class="pda-icon pda-icon-15"
              style="margin-right: 10px"
              v-if="workOrderNum"
              @click="clearInput('workOrderNum')"
            >
              <img src="@/assets/img/icon-close.svg" alt="icon" />
            </div>
          </div>
          <div
            @click="toggleWorkOrderLock"
            style="margin-right: 10px; display: flex; align-items: center"
          >
            <img
              v-if="isWorkOrderLocked"
              src="@/assets/img/lock.svg"
              style="width: 18px; height: 18px"
              alt="lock"
            />
            <img
              v-else
              src="@/assets/img/unlock.svg"
              style="width: 18px; height: 18px"
              alt="unlock"
            />
          </div>
          <div
            class="pda-icon pda-icon-arrow pda-icon-18"
            style="border-radius: 50%; background-color: #fff"
            @click="handleOpenWorkOrder"
          >
            <img src="@/assets/img/icon-arrow.svg" alt="icon" />
          </div>
        </div>
      </div>
      <!-- 箱码 -->
      <div
        class="content-card content-card-expand"
        :class="{ expandTag: expandFlag2 }"
      >
        <div class="pda-input-row pda-input-row-edit">
          <div class="pda-input-row-icon" @click="handleScan('packCodeRef')">
            <img
              src="@/assets/img/add-container-o.svg"
              class="label-icon"
              alt="icon"
            />
          </div>
          <div class="pda-input-row-input">
            <hips-input
              ref="packCodeRef"
              v-model="packCode"
              :placeholder="
                pageText('placeholder.packageBarCode', '请扫描箱码')
              "
              type="text"
              input-align="center"
              @enter="scanBoxCode"
            />
          </div>
          <div class="pda-input-row-icon">
            <div class="pda-icon pda-icon-15" @click="clearPackCode">
              <img src="@/assets/img/icon-close.svg" alt="icon" />
            </div>
          </div>
        </div>
        <div
          v-show="expandFlag2"
          class="pda-expand-group top-border-g pda-expand-group-edit"
        >
          <div class="pda-expand-detail">
            <div class="pda-expand-detail-row">
              <div class="pda-expand-detail-row-item row-item-left">
                {{ pageText("workOrderNum", "包装工单") }}
              </div>
              <div class="pda-expand-detail-row-item row-item-right">
                {{ packData.workOrderNum }}
              </div>
            </div>
            <div class="pda-expand-detail-row">
              <div class="pda-expand-detail-row-item row-item-left">
                {{ pageText("cellMaterialCode", "电芯料号") }}
              </div>
              <div class="pda-expand-detail-row-item row-item-right">
                {{ batteryMaterialNumValue }}
              </div>
            </div>
            <div class="pda-expand-detail-row">
              <div class="pda-expand-detail-row-item row-item-left">
                {{ pageText("specifiedLevel", "电芯等级") }}
              </div>
              <div class="pda-expand-detail-row-item row-item-right">
                {{ packData.specifiedLevel }}
              </div>
            </div>
            <div class="pda-expand-detail-row">
              <div class="pda-expand-detail-row-item row-item-left">
                {{ pageText("customerName", "客户") }}
              </div>
              <div class="pda-expand-detail-row-item row-item-right">
                {{ packData.customerName }}
              </div>
            </div>
          </div>
          <div class="print-icon" @click="handlePrint">
            <img src="@/assets/img/print.png" alt="" />
            <PrintTemplateButtons
              v-show="false"
              ref="printTemplateButtonRef"
              printButtonCode="MES.BZ_MATERIAL_LOT_PRINT"
              :printTemplateCode="specifyTemplateCode"
              :printData="printData"
            />
          </div>
        </div>
        <div class="pda-expand-group-icon" @click="handleExpand('expandFlag2')">
          <div class="pda-icon pda-icon-arrow pda-icon-18">
            <img src="@/assets/img/icon-arrow.svg" alt="icon" />
          </div>
        </div>
      </div>
      <!-- 电芯 -->
      <div class="content-card">
        <div class="pda-input-row pda-input-row-edit">
          <div
            class="pda-input-row-icon"
            @click="handleScan('packageBarCodeRef')"
          >
            <img
              src="@/assets/img/add-container-o.svg"
              class="label-icon"
              alt="icon"
            />
          </div>
          <div class="pda-input-row-input">
            <hips-input
              ref="packageBarCodeRef"
              v-model="identification"
              :placeholder="
                pageText('placeholder.packageBarCode', '请扫描电芯')
              "
              type="text"
              input-align="center"
              @enter="cellScan"
            />
          </div>
          <div class="pda-input-row-icon">
            <div
              class="pda-icon pda-icon-15"
              @click="clearInput('identification')"
            >
              <img src="@/assets/img/icon-close.svg" alt="icon" />
            </div>
          </div>
        </div>
      </div>
      <!-- 待装载和已装载 -->
      <StowageTabs
        ref="stowageTabsRef"
        mainContainerName="hips-view__content"
        currentParentContainerName="pda-view-content"
        :stayList="stayList"
        :hasList="hasList"
        :packingMaterialList="packingMaterialList"
        :loading="loading"
        @deleteLineBarCode="deleteLineBarCode"
        @clearPackCode="clearPackCode"
        @packingMaterialScanCallback="scanPackageCode"
        @tabChange="handleTabChange"
      />
    </div>

    <!-- 按钮 -->
    <div slot="footer" class="pda-footer-container pda-footer-container-fix">
      <!-- 在已装载页签下显示两个按钮 -->
      <div v-if="currentTabIndex === 2" class="button-group">
        <hips-button
          type="primary"
          icon="send"
          size="large"
          :disabled="!equipmentLoginInfo.workcellCode || loading"
          @click="handleStowage"
          class="button-half"
        >
          {{ pageText("button.stowage", "装载") }}
        </hips-button>

        <hips-button
          type="danger"
          icon="delete"
          size="large"
          :disabled="!hasList.length || loading"
          @click="handleBatchUnload"
          class="button-half"
        >
          {{ pageText("button.batchUnload", "一键卸载") }}
        </hips-button>
      </div>

      <!-- 在其他页签下只显示装载按钮 -->
      <hips-button
        v-else
        block
        type="primary"
        icon="send"
        size="large"
        :disabled="!equipmentLoginInfo.workcellCode || loading"
        @click="handleStowage"
      >
        {{ pageText("button.stowage", "装载") }}
      </hips-button>
    </div>

    <!-- 设备登录弹窗 -->
    <DeviceLogin
      ref="deviceLoginRef"
      :infoData="equipmentLoginInfo"
      @selectedFun="selectedFun"
    />

    <!-- 对话框 -->
    <CustomDialog ref="customDialogRef" />

    <!-- 加载动画 -->
    <hips-indicator v-model="loading" pure-background />

    <!-- 工单选择弹窗 -->
    <hips-popup
      v-model="showWorkOrderPicker"
      position="bottom"
      round
      lock-scroll
      safe-area-inset-bottom
    >
      <hips-picker
        v-if="showWorkOrderPicker"
        ref="workOrderPickerRef"
        :title="pageText('selectWorkOrder', '选择工单')"
        :data="workOrderList"
        @confirm="onConfirmWorkOrder"
        @cancel="showWorkOrderPicker = false"
      />
    </hips-popup>
  </hips-view>
</template>

<script>
import {
  Button,
  Checkbox,
  CheckboxGroup,
  Col,
  Dialog,
  Icon,
  Input,
  NavBar,
  Picker,
  Popup,
  Row,
  View,
  Tabs,
  Tab,
  Scroll,
} from "@hips/vue-ui";
import {
  formatterCollections,
  mapLanguageGetters,
  confirmExit,
  scanParse,
} from "@/utils";
import { uniqBy } from "lodash";
import PrintTemplateButtons from "../components/print-template-button/index.vue";

import StowageTabs from "../components/stowage-tabs.vue";
import DeviceLogin from "../components/device-login.vue";
import CustomDialog from "../components/custom-dialog.vue";

import { Store } from "@hips/vue-core";
import { getLookupCode } from "@/apis";
import { getErrorResponse } from "@/utils";

import {
  loginApi,
  materialScanApi,
  packScanApi,
  cellScanApi,
  cellLoadApi,
  cellUnloadApi,
  cellBatchUnloadApi,
  getPrintDataApi,
  getWorkOrderListApi,
} from "../api";

const { mapState, mapActions, mapMutations } = Store;
const modelPrompt = "tarzan.pda.cellPacking";

const page = {
  name: "CellPacking",
  components: {
    [View.name]: View,
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Icon.name]: Icon,
    [Picker.name]: Picker,
    [Checkbox.name]: Checkbox,
    [CheckboxGroup.name]: CheckboxGroup,
    [Row.name]: Row,
    [Col.name]: Col,
    [Input.name]: Input,
    [Dialog.name]: Dialog,
    [Popup.name]: Popup,
    [Tabs.name]: Tabs,
    [Tab.name]: Tab,
    [Scroll.name]: Scroll,
    StowageTabs,
    DeviceLogin,
    CustomDialog,
    PrintTemplateButtons,
  },
  data() {
    return {
      expandFlag: false, // 包材展开控制
      expandFlag2: false, // 箱码展开控制
      materialLotCode: null,
      materialLotData: {},
      packCode: null,
      packData: {},
      loading: false,
      identification: null,
      dialogCheckBoxFlag: false,
      printData: [],
      specifyTemplateCode: "",
      operationId: "",
      workOrder: null,
      workOrderNum: null,
      showWorkOrderPicker: false,
      fullWorkOrderList: [],
      hmeProLineVO: null, // 新增，保存产线信息
      isWorkOrderLocked: false,
      currentTabIndex: 1, // 当前选中的tab索引，默认为待装载tab
    };
  },
  computed: {
    ...mapState("cellPacking", [
      "equipmentLoginInfo",
      "loginInfo",
      "stayList",
      "hasList",
      "unloadList",
      "packingMaterialList",
      "workOrderList",
    ]),
    ...mapLanguageGetters(modelPrompt),
    materialLotValue() {
      const { materialCode, materialName } = this.materialLotData;
      return materialCode || materialName
        ? `${materialCode ?? null}/${materialName ?? null}`
        : null;
    },
    batteryMaterialNumValue() {
      const { cellMaterialCode, cellMaterialName } = this.packData;
      return cellMaterialCode || cellMaterialName
        ? `${cellMaterialCode ?? null}/${cellMaterialName ?? null}`
        : null;
    },
  },
  watch: {},
  mounted() {
    getLookupCode({
      data: `MAX_PACK_LIMIT`,
    }).then((res) => {
      if (!getErrorResponse(res) && res.data[0]) {
        this.updateMaxLimit(res.data[0].value * 1);
      }
    });
    this.updateHips(this.$hips);
  },
  methods: {
    ...mapActions("cellPacking", []),
    ...mapMutations("cellPacking", [
      "updateEquipmentLoginInfo",
      "updateLoginInfo",
      "updateStayAndHasList",
      "updateWorkOrderList",
      "updateMaxLimit",
      "updateHips",
    ]),
    // 退出
    exit() {
      let that = this;
      this.$hips.dialog.confirm({
        closable: true,
        content: that.pageText("delivery.clear.message", "确认退出？"),
        okText: that.pageText("button.confirm", "确认"),
        cancelText: that.pageText("button.cancel", "取消"),
        onOk() {
          confirmExit();
        },
        onCancel() {},
      });
    },
    clearPackCode() {
      this.packCode = "";
      this.packData = {};
      if (!this.isWorkOrderLocked) {
        this.workOrder = null;
        this.workOrderNum = null;
      }
      this.updateStayAndHasList({
        hasList: [],
        stayList: [],
      });
    },
    // 展开收起控制
    handleExpand(flag, val) {
      this[flag] = val ?? !this[flag];
      this.$refs.stowageTabsRef.heightCalcExec();
    },
    // 设备登录选中
    async selectedFun(item) {
      this.loading = true;
      try {
        const res = await loginApi({
          workcellCode: item.workcellCode,
          workcellName: item.workcellName,
        });

        if (res && res.success) {
          this.operationId = res.rows.hmeOperation.operationId;
          this.updateEquipmentLoginInfo(item);
          this.updateLoginInfo(res.rows);
          // 获取工单列表
          if (res.rows.hmeProLineVO) {
            this.hmeProLineVO = res.rows.hmeProLineVO; // 新增，保存产线信息
            await this.getWorkOrderList(res.rows.hmeProLineVO);
            this.$nextTick(() => {
              if (this.$refs.workOrderPickerRef && this.workOrderList.length) {
                this.$refs.workOrderPickerRef.setPickerData(
                  this.workOrderList,
                  [0]
                );
              }
            });
          }
        } else {
          this.$hips.toast(res.message);
        }
        this.loading = false;
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    },
    // 获取工单列表
    async getWorkOrderList(proLineVO) {
      try {
        const res = await getWorkOrderListApi({ hmeProLineVO: proLineVO });
        if (res && res.success) {
          const formattedList = res.rows.map((item) => ({
            code: item.workOrderNum,
            value: item.workOrderNum,
          }));
          this.updateWorkOrderList(formattedList);
          this.fullWorkOrderList = res.rows;
        } else {
          this.$hips.toast(res.message);
        }
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    },
    // 扫描包材
    async scanPackageCode(val) {
      if (!val) {
        this.$hips.toast("请扫描或输入包材!");
        return;
      }

      this.loading = true;
      try {
        console.log(
          {
            ...this.loginInfo,
            materialLotCode: val,
            hmeWoAvailableVO3: this.workOrder,
          },
          "params"
        );
        const res = await materialScanApi({
          ...this.loginInfo,
          materialLotCode: val,
          hmeWoAvailableVO3: this.workOrder,
        });

        if (res && res.success) {
          const list = this.packingMaterialList;
          const record = list.find(
            (item) => item.materialCode === res.rows.rawMaterial.materialCode
          );
          const obj = {
            ...res.rows.rawMaterial.materialLotReturnVO,
            materialLotCode: val,
          };

          if (record) {
            list.unshift({
              ...record,
              lines: uniqBy([...record.lines, obj], "identification"),
            });
          } else {
            list.unshift({
              ...res.rows.rawMaterial,
              lines: [obj],
            });
          }

          console.log("🚀 ~ scanPackageCode ~ list:", list);
          this.updateStayAndHasList({
            packingMaterialList: uniqBy(list, "materialCode"),
          });

          this.$refs.stowageTabsRef.packingMaterialCode = null;
          this.$refs?.stowageTabsRef.$refs.materialLotCodeRef.$refs.input.focus();
        } else {
          this.$hips.toast(res.message);
        }
        this.loading = false;
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    },
    // 扫描箱码
    async scanBoxCode(resetArr) {
      if (!this.packCode) {
        this.$hips.toast("请扫描或输入箱码!");
        return;
      }

      this.loading = true;
      try {
        const res = await packScanApi({
          ...this.loginInfo,
          packCode: this.packCode,
          workOrderNum: this.workOrderNum,
          hmeWoAvailableVO3: this.workOrder,
        });

        if (res && res.success) {
          this.packData = res.rows;
          this.specifyTemplateCode = res.rows.barCodeType;
          this.reset([
            "packCode",
            "packData",
            ...(resetArr?.length ? resetArr : []),
          ]);
          this.updateStayAndHasList({
            hasList: res.rows.cellCodeList.map((i, index) => ({
              lineNumber: index + 1,
              identification: i.identification,
              mainPowderLot: i.mainPowderLot,
              packCode: this.packCode,
            })),
            stayList: [],
          });
          this.handleExpand("expandFlag2", true);

          const list = res.rows.rawMaterialList;
          list.forEach((i) => {
            i.lines = [i.materialLotReturnVO];
          });

          this.updateStayAndHasList({
            packingMaterialList: uniqBy(list, "materialCode"),
          });
        } else {
          this.$hips.toast(res.message);
        }
        this.loading = false;
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    },
    // 扫描电芯
    async cellScan() {
      const existObj = this.hasList.find(
        (i) => i.identification === this.identification
      );
      const repeatObj = this.stayList.find(
        (i) => i.identification === this.identification
      );

      if (!this.identification) {
        this.$hips.toast("请扫描或输入电芯!");
        return;
      }

      if (existObj) {
        if (!this.dialogCheckBoxFlag) {
          this.$refs.customDialogRef.open({
            value: `${this.identification}已装载，是否卸载？`,
            showCheckbox: true,
            asyncClose: true,
            onOk: async () => {
              return this.deleteLineBarCode({
                unloadList: [existObj],
              });
            },
            successCallback: (item) => {
              this.dialogCheckBoxFlag = item.checkBoxFlag;
            },
          });
        } else {
          this.deleteLineBarCode({ hasList: [existObj] });
        }

        return;
      }

      if (repeatObj) {
        this.$refs.stowageTabsRef.handleDelete("stayList", repeatObj);
        return;
      }

      this.loading = true;
      try {
        const res = await cellScanApi({
          ...this.loginInfo,
          hmeWoAvailableVO3: this.workOrder,
          identification: this.identification,
          packCode: this.packCode,
          loadList: this.stayList,
          cellCodeList: this.hasList,
          workOrderNum: this.workOrderNum,
        });

        if (res && res.success) {
          if (!this.packCode) {
            this.packCode = res.rows.packCode;
          }
          console.log("res.rows", res.rows);
          this.packData = {
            ...this.packData,
            workOrderNum: res.rows.workOrderNum,
            cellMaterialCode: res.rows.cellMaterialCode,
            cellMaterialName: res.rows.cellMaterialName,
            specifiedLevel: res.rows.specifiedLevel,
            customerName: res.rows.customerName,
            mainPowderLot: res.rows.mainPowderLot,
          };
          this.updateStayAndHasList({
            stayList: [
              ...this.stayList,
              {
                identification: this.identification,
                lineNumber: this.stayList.length + 1,
                packCode: this.packCode,
                mainPowderLot: res.rows.mainPowderLot,
              },
            ],
          });

          this.identification = null;
          this.$refs.packageBarCodeRef.$refs.input.focus();
        } else {
          if (res.rows) {
            this.$refs.customDialogRef.open({
              value: res.message,
              onOk: () => {
                this.packCode = res.rows.packCode;
                this.updateStayAndHasList({
                  unloadList: this.unloadList.concat([
                    {
                      packCode: res.rows.oldPackCode,
                      identification: this.identification,
                    },
                  ]),
                  stayList: this.stayList.concat([
                    {
                      identification: this.identification,
                      lineNumber: this.stayList.length + 1,
                      packCode: this.packCode,
                      mainPowderLot: res.rows.mainPowderLot,
                    },
                  ]),
                });
              },
            });
          } else {
            this.$hips.toast(res.message);
          }
        }
        this.loading = false;
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    },
    // 装载
    async handleStowage() {
      if (!this.stayList.length) {
        this.$hips.toast("请先扫描电芯!");
        return;
      }

      this.loading = true;
      try {
        const res = await cellLoadApi({
          ...this.loginInfo,
          hmeWoAvailableVO3: this.workOrder,
          loadList: this.stayList,
          unloadList: this.unloadList,
          rawMaterialList: this.packingMaterialList, // todo
          workOrderNum: this.workOrderNum,
        });

        if (res && res.success) {
          this.scanBoxCode(["materialLotCode"]);
        } else {
          this.$hips.toast(res.message);
        }
        this.loading = false;
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    },
    // 输入框清除
    clearInput(field) {
      this[field] = null;
    },
    // tab切换事件处理
    handleTabChange(index) {
      this.currentTabIndex = index;
    },
    // 一键卸载
    async handleBatchUnload() {
      if (!this.hasList.length) {
        this.$hips.toast("没有可卸载的数据!");
        return;
      }

      // 弹出确认对话框
      this.$hips.dialog.confirm({
        title: this.pageText("title.tips", "提示"),
        content: this.pageText(
          "message.confirmBatchUnload",
          `确认要卸载全部${this.hasList.length}个已装载项目吗？`
        ),
        okText: this.commonText("button.confirm", "确认"),
        cancelText: this.commonText("button.cancel", "取消"),
        showCancelButton: true,
        onOk: async () => {
          await this.executeBatchUnload();
        },
        onCancel: () => {},
        closable: false,
      });
    },
    // 执行批量卸载
    async executeBatchUnload() {
      this.loading = true;
      try {
        const res = await cellBatchUnloadApi({
          ...this.loginInfo,
          hmeWoAvailableVO3: this.workOrder,
          unloadList: this.hasList, // 传入所有已装载的数据
        });

        if (res && res.success) {
          this.$hips.toast("批量卸载成功!");
          this.scanBoxCode(); // 重新扫描刷新数据
        } else {
          this.$hips.toast(res.message);
        }
        this.loading = false;
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    },
    // 装载行删除
    async deleteLineBarCode(valObj) {
      if (valObj?.stayList || valObj?.packingMaterialList) {
        this.updateStayAndHasList(valObj);

        return;
      }

      this.loading = true;
      try {
        const res = await cellUnloadApi({
          ...this.loginInfo,
          hmeWoAvailableVO3: this.workOrder,
          unloadList: valObj.unloadList,
        });

        if (res && res.success) {
          this.$hips.toast(
            `${this.hasList[0].identification}已装载数据删除成功`
          );
          this.scanBoxCode();
        } else {
          this.$hips.toast(res.message);
        }
        this.loading = false;
        return res;
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    },
    // 打印
    async handlePrint() {
      if (this.hasList.length && this.packCode) {
        try {
          const res = await getPrintDataApi({
            operationId: this.operationId,
            packCode: this.packCode,
          });
          if (!res?.message) {
            this.printData = [res];
            this.$refs.printTemplateButtonRef.handleOpenPrintTemplatePopup();
            // 监听打印模板选择事件
            this.$refs.printTemplateButtonRef.$on(
              "print-template-selected",
              (templateCode) => {
                console.log("templateCode", templateCode);
                if (templateCode === "MES-DXZX3" && res.cellQty === 130) {
                  const { ...restData } = res;
                  this.printData = [restData];
                }
              }
            );
          } else {
            this.$hips.toast(res.message);
          }
        } catch (error) {
          console.log(error);
        }
      } else {
        this.$hips.toast("装箱后才能补打箱码");
      }
    },
    // 扫码
    async handleScan(params) {
      const res = await scanParse();
      if (params === "materialLotCodeRef") {
        this.materialLotCode = res.result;
        this.scanPackageCode();
      } else if (params === "packCodeRef") {
        this.packCode = res.result;
        this.scanBoxCode();
      } else if (params === "packageBarCodeRef") {
        this.identification = res.result;
        this.cellScan();
      }
    },
    // 重置
    reset(arr) {
      const fields = {
        materialLotCode: null,
        packCode: null,
        identification: null,
        materialLotData: [],
        packData: [],
        updateStayAndHasList: () => {
          this.updateStayAndHasList({
            stayList: [],
            hasList: [],
          });
        },
      };

      Object.keys(fields)
        .filter((key) => !arr.includes(key))
        .forEach((key) => {
          if (typeof fields[key] === "function") {
            fields[key]();
          } else {
            this[key] = fields[key];
          }
        });
    },
    toggleWorkOrderLock() {
      this.isWorkOrderLocked = !this.isWorkOrderLocked;
    },
    // 打开工单选择弹窗，点击时每次都重新请求工单列表
    async handleOpenWorkOrder() {
      if (this.hmeProLineVO) {
        this.loading = true;
        await this.getWorkOrderList(this.hmeProLineVO);
        this.loading = false;
        if (this.workOrderList.length) {
          this.showWorkOrderPicker = true;
        } else {
          this.$hips.toast("暂无工单可选");
        }
      } else {
        this.$hips.toast("请先设备登录");
      }
    },
    // 确认选择工单
    onConfirmWorkOrder(selectedValue) {
      console.log("selectedValue", selectedValue);
      this.workOrderNum = selectedValue[0].value.code;
      this.workOrder = this.fullWorkOrderList.find(
        (item) => item.workOrderNum === this.workOrderNum
      );
      if (this.workOrder && this.workOrder.hmeWoAvailableVO3) {
        this.packData = {
          ...this.packData,
          hmeWoAvailableVO3: this.workOrder.hmeWoAvailableVO3,
        };
      }
      this.showWorkOrderPicker = false;
    },
  },
};

export default formatterCollections({
  code: [modelPrompt, "tarzan.pda.common"],
})(page);
</script>

<style scoped lang="stylus">
@import '../../../style/pda.styl';

>>> .pda-view-content-edit {
  padding-bottom: 8px;

  .content-card {
    padding: 8px;
    background-color: #fff;
    margin-bottom: 8px;
    border-radius: 4px;

    .pda-input-row-edit {
      border: 2px solid #7ca4ff;
      background-color: #e1eaff;
      border-radius: 8px;

      .hips-input, input {
        background: none;
      }
    }

    .pda-expand-group-edit {
      justify-content: flex-end;
      align-items: flex-start;
    }

    .print-icon {
      width: 50px;
      height: 50px;
      position: relative;
      top: 0;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .content-card-expand {
    position: relative;

    .pda-input-row {
      width: calc(100% - 30px);
      transition: width .5s;
    }
    .pda-expand-group-icon{
      position: absolute;
      right: 8px;
      top: calc(50% - 7.5px);
      transition: top .5s;
    }
  }

  .expandTag {
      .pda-input-row {
        width: 100%;
        transition: width .5s;
      }
      .pda-expand-group-icon{
        position: absolute;
        right: 8px;
        top: calc(100% - 18px - 8px);
        transition: top .5s;

        >>> .hips-icon {
          transform: rotate(-90deg);
        }
        .pda-icon-arrow{
          transform: rotate(-180deg);
        }
      }
    }
}

.nav-bar-right {
  font-size: 14px;
  color: #7ca4ff;
}

.button-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.button-half {
  flex: 1;
}
</style>
