### `模块名汇总`

```
physical-transfer
physical-transfer-mes
site-change
container-register
material-lot-merge
material-lot-split
container-load-or-unload
storage-apply
purchase-shelves
purchase-return
purchase-new-receive
storage-receive
storage-recall
storage-putaway
product-pick
outsourcing
material-sign-off
picking
purchase-revoke
product-delivery
first-count-execution
recounts-execution
mixed-send
mixed-receive
return-execute
send-execute
storage-return-receive
receive-execute
container-load-register
```


### `打包的脚本`

```
rm -rf dist.tar.gz
yarn --registry=http://nexus.saas.hand-china.com/repository/hzero-npm-group/

yarn build:${BUILD_MODULE}:prod
tar -zcvf dist.tar.gz ./dist/${BUILD_MODULE}
cp dist.tar.gz /data/hippius/mobile
cd /data/hippius/mobile
tar -zxvf dist.tar.gz ./;
rm -rf ${BUILD_MODULE}
mv dist/${BUILD_MODULE} ${BUILD_MODULE}
rm -rf dist
rm -fr dist.tar.gz;

```


### `nginx配置`

```
server {
    listen       9050;
    server_name  localhost;

    location / {
        root  /data/hippius/mobile;
        try_files $uri /index.html;
        index  index.html index.htm;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }
}
```