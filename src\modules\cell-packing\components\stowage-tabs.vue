<template>
  <div class="pda-card pda-card-edit">
    <hips-tabs
      v-model="active"
      color="#458BF7"
      :swipe-threshold="0"
      class="pda-detail-tab"
      id="stowageTabId"
      @change="handleTabChange"
    >
      <hips-tab :title="`${pageText('was.packingMaterial', '包材')}`">
        <div
          class="pda-card no-padding pda-card-scroll-area"
          :style="{
            'overflow-x': 'hidden',
            'overflow-y': 'auto',
            'overflow-scrolling': 'touch',
            transition: 'height 0.3s linear',
            height: `${scrollHeight}px`,
          }"
        >
          <div
            class="pda-view-content pda-view-content-edit"
            style="height: 100%; overflow-y: scroll"
          >
            <div
              class="content-card"
              style="
                position: sticky;
                top: 0;
                z-index: 999;
                background-color: #fff;
              "
            >
              <div class="pda-input-row pda-input-row-edit">
                <div
                  class="pda-input-row-icon"
                  @click="handleScan('materialLotCodeRef')"
                >
                  <img
                    src="@/assets/img/add-container-o.svg"
                    class="label-icon"
                    alt="icon"
                  />
                </div>
                <div class="pda-input-row-input">
                  <hips-input
                    ref="materialLotCodeRef"
                    v-model="packingMaterialCode"
                    :placeholder="
                      pageText('placeholder.packageBarCode', '请扫描包材条码')
                    "
                    type="text"
                    input-align="center"
                    @enter="
                      queryPackingMaterial(
                        packingMaterialCode,
                        'materialLotCodeRef'
                      )
                    "
                  />
                </div>
                <div class="pda-input-row-icon">
                  <div
                    class="pda-icon pda-icon-15"
                    @click="clearInput('packingMaterialCode')"
                  >
                    <img src="@/assets/img/icon-close.svg" alt="icon" />
                  </div>
                </div>
              </div>
            </div>
            <div
              v-for="(item, index) in packingMaterialList"
              :key="item.lineNumber"
              class="content-card"
              style="padding: 8px; border: 2px solid #e1e1e1"
              :style="index > 0 ? 'margin-top: 8px' : ''"
            >
              <div
                style="font-size: 14px; font-weight: bold; margin-bottom: 8px"
              >
                {{ `${item.materialCode}/${item.materialName}` }}
              </div>
              <div
                class="pda-expand-group top-border-g pda-expand-group-edit"
                style="padding-bottom: 0"
              >
                <hips-row
                  v-for="item2 in item.lines"
                  :key="item2.lineNumber"
                  class="stowage-tab-row"
                  style="
                    width: 100%;
                    margin-top: 0;
                    padding-top: 0;
                    padding-bottom: 0;
                  "
                >
                  <hips-col
                    span="22"
                    style="flex-direction: column; align-items: flex-start"
                  >
                    <div class="stowage-tab-row-font">
                      {{ item2.materialLotCode }}
                    </div>
                    <div class="stowage-tab-row-font">
                      批次: {{ item2.lot }}
                    </div>
                  </hips-col>
                  <hips-col
                    span="2"
                    @click.native="
                      handleDelete(
                        'packingMaterialList',
                        item2,
                        'materialLotCode'
                      )
                    "
                  >
                    <img src="@/assets/img/icon-close.svg" height="50%" />
                  </hips-col>
                </hips-row>
              </div>
            </div>
            <PdaCardEmpty
              v-if="!packingMaterialList.length"
              show
              :desc="
                pageText('emptyNote.packingMaterial', '请扫描条码添加包材对象')
              "
            />
          </div>
        </div>
      </hips-tab>

      <hips-tab
        :title="`${pageText('wait.stocking', '待装载')}(${stayList.length})`"
      >
        <div
          class="pda-card no-padding pda-card-scroll-area"
          :style="{
            'overflow-x': 'hidden',
            'overflow-y': 'auto',
            'overflow-scrolling': 'touch',
            transition: 'height 0.3s linear',
            height: `${scrollHeight}px`,
          }"
        >
          <hips-scroll
            v-if="stayList.length"
            :scroll-events="['scroll', 'scroll-end']"
            :scroll-options="scrollOption"
          >
            <hips-row
              v-for="item in stayList"
              :key="item.lineNumber"
              class="stowage-tab-row"
            >
              <hips-col span="3"> {{ item.lineNumber }} </hips-col>
              <hips-col span="19"> {{ item.identification }} </hips-col>
              <hips-col
                span="2"
                @click.native="!loading && handleDelete('stayList', item)"
              >
                <img
                  src="@/assets/img/icon-close.svg"
                  height="70%"
                  :style="{
                    opacity: loading ? 0.5 : 1,
                    cursor: loading ? 'not-allowed' : 'pointer',
                  }"
                />
              </hips-col>
            </hips-row>
          </hips-scroll>
          <PdaCardEmpty
            v-else
            show
            :desc="pageText('emptyNote.stocking', '请扫描条码添加装载对象')"
          />
        </div>
      </hips-tab>
      <hips-tab
        :title="`${pageText('was.deliver', '已装载')}(${hasList.length})`"
      >
        <div
          class="pda-card no-padding pda-card-scroll-area"
          :style="{
            'overflow-x': 'hidden',
            'overflow-y': 'auto',
            'overflow-scrolling': 'touch',
            transition: 'height 0.3s linear',
            height: `${scrollHeight}px`,
          }"
        >
          <hips-scroll
            v-if="hasList.length"
            ref="scrollWait"
            :scroll-events="['scroll', 'scroll-end']"
            :scroll-options="scrollOption"
          >
            <hips-row
              v-for="item in hasList"
              :key="item.lineNumber"
              class="stowage-tab-row"
            >
              <hips-col span="3"> {{ item.lineNumber }} </hips-col>
              <hips-col span="19"> {{ item.identification }} </hips-col>
              <hips-col
                span="2"
                @click.native="!loading && handleDelete('hasList', item)"
              >
                <img
                  src="@/assets/img/icon-close.svg"
                  height="70%"
                  :style="{
                    opacity: loading ? 0.5 : 1,
                    cursor: loading ? 'not-allowed' : 'pointer',
                  }"
                />
              </hips-col>
            </hips-row>
          </hips-scroll>
          <PdaCardEmpty
            v-else
            show
            :desc="pageText('emptyNote.deliver', '请扫描条码添加装载对象')"
          />
        </div>
      </hips-tab>
    </hips-tabs>
  </div>
</template>

<script>
import {
  Button,
  Checkbox,
  CheckboxGroup,
  Col,
  Dialog,
  Icon,
  Input,
  NavBar,
  Picker,
  Popup,
  Row,
  View,
  Tabs,
  Tab,
  Scroll,
} from "@hips/vue-ui";
import PdaCardEmpty from "@/components/pda-card-empty";
import { mapLanguageGetters, scanParse } from "@/utils";

const modelPrompt = "tarzan.pda.cellPacking";

export default {
  name: "StowageTabs",
  components: {
    [View.name]: View,
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Icon.name]: Icon,
    [Picker.name]: Picker,
    [Checkbox.name]: Checkbox,
    [CheckboxGroup.name]: CheckboxGroup,
    [Row.name]: Row,
    [Col.name]: Col,
    [Input.name]: Input,
    [Dialog.name]: Dialog,
    [Popup.name]: Popup,
    [Tabs.name]: Tabs,
    [Tab.name]: Tab,
    [Scroll.name]: Scroll,
    [PdaCardEmpty.name]: PdaCardEmpty,
  },
  props: {
    mainContainerName: {
      type: String,
      require: true,
      default: "",
    },
    currentParentContainerName: {
      type: String,
      require: true,
      default: "",
    },
    stayList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    hasList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    packingMaterialList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      active: 0,
      scrollHeight: 0,
      scrollOption: {
        scrollbar: {
          fade: false,
        },
      },
      timer: null,
      packingMaterialCode: null,
    };
  },
  computed: {
    ...mapLanguageGetters(modelPrompt),
  },
  watch: {},
  mounted() {
    this.heightCalcExec();
    window.addEventListener("resize", this.heightCalcExec);
  },
  methods: {
    // 高度计算
    heightCalc() {
      // 主显示容器
      const { offsetParent, className } = document.getElementsByClassName(
        this.mainContainerName
      )[0];
      const showHeight = [...(offsetParent.childNodes || [])].reduce((a, b) => {
        const { marginBottom, marginTop } = window.getComputedStyle(b);
        const topVal = parseFloat(marginTop);
        const bottomVal = parseFloat(marginBottom);

        if (b.className.indexOf(className) === -1) {
          return a - (b.clientHeight + topVal + bottomVal);
        }

        return a;
      }, offsetParent.clientHeight || 0);
      const mainHeihgt = showHeight;

      // 当前tabs父容器
      const ele = document.getElementsByClassName(
        this.currentParentContainerName
      )[0];
      const chilHeightTotal = [...(ele?.childNodes || [])].reduce((a, b) => {
        const { marginBottom, marginTop, paddingBottom, paddingTop } =
          window.getComputedStyle(b);
        const topVal = parseFloat(marginTop);
        const bottomVal = parseFloat(marginBottom);
        const paddBottomVal = parseFloat(paddingBottom);
        const paddTopVal = parseFloat(paddingTop);

        if (b.className.indexOf("pda-card") === -1) {
          return a + b.clientHeight + topVal + bottomVal;
        }

        return a + (paddBottomVal + paddTopVal);
      }, 0);
      const { paddingBottom, paddingTop } = window.getComputedStyle(ele);

      // tabs容器
      const { childNodes } = document.getElementById("stowageTabId");
      const { marginBottom, marginTop } = window.getComputedStyle(
        childNodes[0]
      );
      const tabHeight =
        childNodes[0].clientHeight +
        parseFloat(marginBottom) +
        parseFloat(marginTop);

      this.scrollHeight =
        mainHeihgt -
        (chilHeightTotal + parseFloat(paddingTop) + parseFloat(paddingBottom)) -
        tabHeight;
    },
    // 执行高度计算
    heightCalcExec() {
      if (this.timer) {
        clearTimeout(this.timer);
      }

      this.timer = setTimeout(() => {
        this.heightCalc();
      }, 300);
    },
    // 删除
    handleDelete(listName, item, field = "identification") {
      this.$hips.dialog.confirm({
        title: this.pageText("title.tips", "提示"),
        content: this.pageText(
          "message.confirmDeleteCurrentLine",
          `确认删除${item[field]}?`
        ),
        okText: this.commonText("button.confirm", "确认"),
        cancelText: this.commonText("button.cancel", "取消"),
        showCancelButton: true,
        onOk: async () => {
          let arr = [];

          if (listName === "packingMaterialList") {
            arr = this[listName].reduce((a, b) => {
              const lines = b.lines.filter((i) => i[field] !== item[field]);

              if (lines?.length) {
                return a.concat([{ ...b, lines }]);
              }

              return a;
            }, []);
          } else if (listName === "hasList") {
            arr = this[listName].filter((i) => i[field] === item[field]);
          } else {
            arr = this[listName].filter((i) => i[field] !== item[field]);
          }

          if (listName === "stayList") {
            if (arr.length === 0) {
              this.$emit("clearPackCode");
            }
          }

          this.$emit("deleteLineBarCode", {
            [listName === "hasList" ? "unloadList" : listName]: arr,
          });
        },
        onCancel: () => {},
        closable: false,
      });
    },
    // 包材查询
    async queryPackingMaterial(val, refName) {
      // 防抖
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        this.$emit("packingMaterialScanCallback", val, refName);
      }, 500);
    },
    // 扫码
    async handleScan(refName) {
      const res = await scanParse();

      this.packingMaterialCode = res.result;
      this.queryPackingMaterial(res.result, refName);
    },
    // 输入框清除
    clearInput(field, item) {
      if (item) {
        this.$set(item, field, null);
      }
      this[field] = null;
    },
    // tab切换事件
    handleTabChange(index) {
      this.$emit("tabChange", index);
    },
  },
};
</script>

<style scoped lang="stylus">
@import '../../../style/pda.styl';

.pda-card-edit {
  padding-top: 0 !important;

  >>> .stowage-tab-row {
    height: 46px;
    padding: 8px;
    margin-top: 8px;
    background-color: #f3f5fa;
    border-radius: 4px;

    .hips-col {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .stowage-tab-row-font {
        height: 50%;
        line-height: 1.5;
        font-size: 13px;

        &:first-child {
          line-height: 1.8;
        }
      }
    }
  }

  >>> .pda-view-content-edit {
    padding-bottom: 8px;
    padding-top: 0;

    .content-card {
      padding: 8px 0;
      background-color: #fff;
      margin-bottom: 0;
      border-radius: 4px;

      .pda-input-row-edit {
        border: 2px solid #7ca4ff;
        background-color: #e1eaff;
        border-radius: 8px;
        height: 39px;

        .hips-input, .hips-input__input-value, input {
          height: 100%;
          background: none;
        }
      }

      .pda-expand-group-edit {
        flex-direction: column;
        justify-content: flex-end;
        align-items: flex-start;
        gap: 8px;
      }
    }

    .content-card-expand {
      position: relative;

      .pda-input-row {
        width: calc(100% - 30px);
        transition: width .5s;
      }
      .pda-expand-group-icon{
        position: absolute;
        right: 0;
        top: calc(50% - 7.5px);
        transition: top .5s;
      }
    }

    .expandTag {
      .pda-input-row {
        width: 100%;
        transition: width .5s;
      }
      .pda-expand-group-icon{
        position: absolute;
        right: 0;
        top: calc(100% - 18px - 8px);
        transition: top .5s;

        >>> .hips-icon {
          transform: rotate(-90deg);
        }
        .pda-icon-arrow{
          transform: rotate(-180deg);
        }
      }
    }
  }
}
</style>
