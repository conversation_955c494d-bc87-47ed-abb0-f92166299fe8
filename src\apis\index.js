/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2021-12-29 14:18:41
 * @LastEditTime: 2023-05-25 15:49:41
 * @LastEditors: <<EMAIL>>
 */

import BASIC from "@/utils/config";
import { getTenantId, getCurrentLanguage } from "@/utils";
import http from "@/request";
import httpNew from "@/requestNew";

const tenantId = getTenantId();

// 由于在功能初始化时就会使用这个文件，直接定义tenantId可能会出现取不到tenantId的问题
// const tenantId = getTenantId();

export { default as BarcodeHelper } from "./CacheHelper/BarcodeHelper"; //  存物料批缓存
export { default as OrderHelper } from "./CacheHelper/OrderHelper"; //  存物料批缓存

/**
 * 查询用户默认站点
 */
export function getUserDefaultSite() {
  const url = `${BASIC.BASIC_MODEL}/v1/${tenantId}/mt-user-organization/user/default/site/app`;
  return http.get(url);
}

/**
 * 多语言查询
 */
export async function getLanguage(params) {
  const data = { params };
  const language = getCurrentLanguage();
  if (!tenantId || !language) {
    return {};
  }
  const url = `/hpfm/v1/${tenantId}/prompt/${language}`;
  return http.get(url, data);
}

/**
 * lov查询
 */

export async function getLovViewRequest(params) {
  const url = `/hpfm/v1/${tenantId}/lov-view/info`;
  return http.get(url, {
    params: {
      tenantId,
      ...params,
    },
  });
}

/**
 * lov明细查询
 */

export async function getLovDetailRequest(params) {
  const { requestMethod, queryUrl, size, page, ...other } = params;
  const url = (queryUrl || "").replace("{organizationId}", tenantId);
  if ((requestMethod || "").toUpperCase() === "POST") {
    return http.post(url, {
      size,
      page,
      ...other,
      tenantId,
    });
  } else {
    return http.get(url, {
      params: {
        size,
        page,
        ...other,
        tenantId,
      },
    });
  }
}

/**
 * sql类lov明细查询
 */
export async function getLovSQLRequest(code, params = {}) {
  const url = `/hpfm/v1/${tenantId}/lovs/data`;
  return http.get(url, {
    params: {
      lovCode: code,
      tenantId,
      ...params,
    },
  });
}

/**
 * mt-gen-status查询
 */

export async function getStatusGroupRequest(params) {
  const url = `${BASIC.BASIC_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui`;
  return http.get(url, {
    params,
  });
}

/**
 * mt-gen-type查询
 */

// lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=NC_REPORT_TYPE`,
export async function getTypeGroupRequest(params) {
  const url = `${BASIC.BASIC_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`;
  return http.get(url, {
    params,
  });
}

/**
 * 物料版本查询
 */

// ${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui
// 参数 tenantId  siteIds materialId
export async function getRevisionCodeRequest(params) {
  const url = `${BASIC.BASIC_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`;
  return http.get(url, {
    ...params,
    tenantId,
  });
}

/**
 * 查询用户默认站点
 */

export async function getUserDefaultSiteRequest() {
  const url = `${BASIC.BASIC_MODEL}/v1/${tenantId}/mt-user-organization/user/default/site/ui`;
  return http.get(url, {
    tenantId,
  });
}

// 检验平台-上传
export async function handleOnUploadFiles(params) {
  const url = `/hfle/v1/${tenantId}/files/attachment/multipart`;
  return http.post(url, params);
}

// 查询存储的文件==
export async function handleFetchUploadFiles(params) {
  const data = { params };
  const url = `/hfle/v1/${tenantId}/files/${params.uuid}/file?attachmentUUID=${params.uuid}`;
  return http.get(url, data);
}

// 文件删除
export async function deleteUploadFile(params) {
  const url = `/hfle/v1/${tenantId}/files/delete-by-uuidurl?attachmentUUID=${params.uuid}`;
  return http.post(url, params.fileData);
}

// 文件复制
export async function copyFile(params) {
  const url = `/hfle/v1/${tenantId}/files/copy-file`;
  return http.post(url, params);
}

// 获取独立值集
export async function getLookupCode(params) {
  const url = `/hpfm/v1/${tenantId}/lovs/value/batch`;
  return httpNew.get(url, params);
}

export async function fetchStatusList(requestUrl) {
  const url = requestUrl;
  return httpNew.get(url);
}
