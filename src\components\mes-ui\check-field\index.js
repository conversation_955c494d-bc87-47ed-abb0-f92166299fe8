import { Tag } from "@hips/vue-ui";
import { isArray } from "lodash";
import { Store } from "@hips/vue-core";
import "./index.styl";

import { confirmExit, mapLanguageGetters } from "@/utils";

const { mapState } = Store;
const modelPrompt = "tarzan.pda.common.component.checkField";

export default {
  name: "checkField",
  components: {
    [Tag.name]: Tag,
  },
  model: {
    prop: "fatherValue",
    event: "select",
  },
  props: {
    required: <PERSON><PERSON><PERSON>,
    fatherValue: [String, Array, Number],
    disabled: Boolean,
    multiple: Boolean,
    icon: {
      type: String,
      default: "add-container",
    },
    label: {
      type: String,
      default: undefined,
    },
    options: Array, // 下拉框的配置项
    selectOptions: {
      type: Object,
      default: () => {
        return {
          valueField: "value", // 值字段配置
          displayField: "meaning", // 显示字段配置
        };
      },
    },
  },

  computed: {
    ...mapState("loadingStore", {
      globalLoading: (state) => state.global,
    }),
    ...mapLanguageGetters(modelPrompt),
  },

  methods: {
    exit() {
      confirmExit();
    },

    select(item) {
      if (this.disabled) {
        return;
      }
      if (this.multiple) {
        if (this.fatherValue.includes(item[this.selectOptions.valueField])) {
          this.$emit(
            "select",
            this.fatherValue.filter(
              (e) => e !== item[this.selectOptions.valueField]
            )
          );
        } else {
          this.$emit(
            "select",
            this.fatherValue.concat(item[this.selectOptions.valueField])
          );
        }
      } else {
        if (this.fatherValue === item[this.selectOptions.valueField]) {
          this.$emit("select", "");
        } else {
          this.$emit("select", item[this.selectOptions.valueField]);
        }
      }
    },

    getCurrentClass(record) {
      if (
        (typeof this.fatherValue === "string" &&
          this.fatherValue === record[this.selectOptions.valueField]) ||
        (isArray(this.fatherValue) &&
          this.fatherValue.includes(record[this.selectOptions.valueField]))
      ) {
        return "tag-blue";
      }
      return "tag-grey";
    },
  },

  render() {
    return (
      <field
        required={this.required}
        iconProps={this.iconProps}
        rightIconOptions={this.rightIconOptions}
        label={this.label}
        disabled={this.disabled}
        class="mes-ui-check-field"
      >
        <template slot="field">
          <div
            class={`pda-input-row-input ${
              this.label ? "pda-input-row-input-right-algin" : ""
            }`}
          >
            {this.options.map((e) => (
              <span
                class="mes-ui-check-field-item"
                vOn:click={() => this.select(e)}
              >
                <hips-tag class={this.getCurrentClass(e)}>
                  {e[this.selectOptions.displayField]}
                </hips-tag>
              </span>
            ))}
          </div>
        </template>
      </field>
    );
  },
};
