<svg xmlns="http://www.w3.org/2000/svg" width="102.459" height="81.15" viewBox="0 0 102.459 81.15">
  <defs>
    <style>
      .cls-1 {
        fill: #e4efff;
        opacity: 0.51;
      }

      .cls-2 {
        fill: #94b0f5;
      }

      .cls-3 {
        fill: #0060f5;
        opacity: 0.44;
      }

      .cls-4 {
        fill: #d8e6ff;
      }

      .cls-5 {
        fill: #9b42df;
      }

      .cls-6 {
        fill: #458bf7;
        opacity: 0.82;
      }

      .cls-7 {
        fill: #fff;
      }

      .cls-8 {
        fill: #3a364e;
      }
    </style>
  </defs>
  <g id="组_128" data-name="组 128" transform="translate(0)">
    <path id="路径_36" data-name="路径 36" class="cls-1" d="M9.848,66.345a15.76,15.76,0,0,0,4.878-13.38c-.708-5.688-.386-11.385,3.077-15.737s9.531-6.156,15.016-5.243,10.363,4.245,13.973,8.475c1.317,1.543,2.53,3.249,4.266,4.3,2.782,1.686,6.348,1.35,9.46.4s6.067-2.453,9.277-2.985c5.126-.852,10.459.918,14.627,4.02a35.549,35.549,0,0,1,9.726,12.018c2.315,4.333,4.078,9.682,1.791,14.032-.859,1.633-2.26,3.049-2.54,4.873-.4,2.6,1.607,4.917,3.644,6.576s4.394,3.277,5.153,5.792c1.081,3.583-1.533,7.132-3.976,9.966-2.166,2.512-4.415,5.088-7.386,6.561a20.761,20.761,0,0,1-7.848,1.763,61.017,61.017,0,0,1-28.465-4.938,50.722,50.722,0,0,0-8.405-3.331c-3.347-.8-6.836-.611-10.274-.475-8.864.354-18.106.286-26.088-3.584-3.6-1.748-6.955-4.375-8.638-8.01-2.846-6.148-.022-13.888,5.307-18.072.985-.773,2.2-1.862,3.427-3.02Z" transform="translate(0.013 -29.237)"/>
    <path id="路径_37" data-name="路径 37" class="cls-2" d="M292.048,261.891m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,291.21,261.891Z" transform="translate(-268.131 -240.364)"/>
    <path id="路径_38" data-name="路径 38" class="cls-2" d="M330.853,261.891m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,330.015,261.891Z" transform="translate(-303.861 -240.364)"/>
    <path id="路径_39" data-name="路径 39" class="cls-2" d="M366.132,261.891m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,365.293,261.891Z" transform="translate(-336.343 -240.364)"/>
    <path id="路径_40" data-name="路径 40" class="cls-2" d="M401.409,261.89m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,400.57,261.89Z" transform="translate(-368.824 -240.364)"/>
    <path id="路径_41" data-name="路径 41" class="cls-2" d="M440.214,261.891m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,439.375,261.891Z" transform="translate(-404.554 -240.364)"/>
    <path id="路径_42" data-name="路径 42" class="cls-2" d="M475.491,261.891m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,474.652,261.891Z" transform="translate(-437.036 -240.364)"/>
    <path id="路径_43" data-name="路径 43" class="cls-2" d="M256.772,261.89m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,255.933,261.89Z" transform="translate(-235.649 -240.364)"/>
    <path id="路径_44" data-name="路径 44" class="cls-2" d="M292.048,297.168m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,291.21,297.168Z" transform="translate(-268.131 -272.846)"/>
    <path id="路径_45" data-name="路径 45" class="cls-2" d="M330.853,297.168m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,330.015,297.168Z" transform="translate(-303.861 -272.846)"/>
    <path id="路径_46" data-name="路径 46" class="cls-2" d="M366.132,297.168m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,365.293,297.168Z" transform="translate(-336.343 -272.846)"/>
    <path id="路径_47" data-name="路径 47" class="cls-2" d="M401.409,297.167m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,400.57,297.167Z" transform="translate(-368.824 -272.845)"/>
    <path id="路径_48" data-name="路径 48" class="cls-2" d="M440.214,297.168m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,439.375,297.168Z" transform="translate(-404.554 -272.846)"/>
    <path id="路径_49" data-name="路径 49" class="cls-2" d="M475.491,297.168m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,474.652,297.168Z" transform="translate(-437.036 -272.846)"/>
    <path id="路径_50" data-name="路径 50" class="cls-2" d="M256.772,297.167m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,255.933,297.167Z" transform="translate(-235.649 -272.845)"/>
    <path id="路径_51" data-name="路径 51" class="cls-2" d="M292.048,332.444m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,291.21,332.444Z" transform="translate(-268.131 -305.327)"/>
    <path id="路径_52" data-name="路径 52" class="cls-2" d="M330.853,332.444m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,330.015,332.444Z" transform="translate(-303.861 -305.327)"/>
    <path id="路径_53" data-name="路径 53" class="cls-2" d="M366.132,332.444m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,365.293,332.444Z" transform="translate(-336.343 -305.327)"/>
    <path id="路径_54" data-name="路径 54" class="cls-2" d="M401.409,332.444m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,400.57,332.444Z" transform="translate(-368.824 -305.327)"/>
    <path id="路径_55" data-name="路径 55" class="cls-2" d="M440.214,332.444m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,439.375,332.444Z" transform="translate(-404.554 -305.327)"/>
    <path id="路径_56" data-name="路径 56" class="cls-2" d="M475.491,332.444m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,474.652,332.444Z" transform="translate(-437.036 -305.327)"/>
    <path id="路径_57" data-name="路径 57" class="cls-2" d="M256.772,332.444m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,255.933,332.444Z" transform="translate(-235.649 -305.327)"/>
    <path id="路径_58" data-name="路径 58" class="cls-2" d="M292.048,367.722m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,291.21,367.722Z" transform="translate(-268.131 -337.809)"/>
    <path id="路径_59" data-name="路径 59" class="cls-2" d="M330.853,367.722m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,330.015,367.722Z" transform="translate(-303.861 -337.809)"/>
    <path id="路径_60" data-name="路径 60" class="cls-2" d="M366.132,367.722m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,365.293,367.722Z" transform="translate(-336.343 -337.809)"/>
    <path id="路径_61" data-name="路径 61" class="cls-2" d="M401.409,367.722m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,400.57,367.722Z" transform="translate(-368.824 -337.809)"/>
    <path id="路径_62" data-name="路径 62" class="cls-2" d="M440.214,367.722m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,439.375,367.722Z" transform="translate(-404.554 -337.809)"/>
    <path id="路径_63" data-name="路径 63" class="cls-2" d="M475.491,367.722m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,474.652,367.722Z" transform="translate(-437.036 -337.809)"/>
    <path id="路径_64" data-name="路径 64" class="cls-2" d="M256.772,367.722m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,255.933,367.722Z" transform="translate(-235.649 -337.809)"/>
    <path id="路径_65" data-name="路径 65" class="cls-2" d="M292.048,403m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,291.21,403Z" transform="translate(-268.131 -370.29)"/>
    <path id="路径_66" data-name="路径 66" class="cls-2" d="M330.853,403m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,330.015,403Z" transform="translate(-303.861 -370.29)"/>
    <path id="路径_67" data-name="路径 67" class="cls-2" d="M366.132,403m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,365.293,403Z" transform="translate(-336.343 -370.29)"/>
    <path id="路径_68" data-name="路径 68" class="cls-2" d="M401.409,403m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,400.57,403Z" transform="translate(-368.824 -370.291)"/>
    <path id="路径_69" data-name="路径 69" class="cls-2" d="M440.214,403m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,439.375,403Z" transform="translate(-404.554 -370.29)"/>
    <path id="路径_70" data-name="路径 70" class="cls-2" d="M475.491,403m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,474.652,403Z" transform="translate(-437.036 -370.29)"/>
    <path id="路径_71" data-name="路径 71" class="cls-2" d="M256.772,403m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,255.933,403Z" transform="translate(-235.649 -370.29)"/>
    <path id="路径_72" data-name="路径 72" class="cls-2" d="M292.048,438.276m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,291.21,438.276Z" transform="translate(-268.131 -402.772)"/>
    <path id="路径_73" data-name="路径 73" class="cls-2" d="M330.853,438.276m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,330.015,438.276Z" transform="translate(-303.861 -402.772)"/>
    <path id="路径_74" data-name="路径 74" class="cls-2" d="M366.132,438.276m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,365.293,438.276Z" transform="translate(-336.343 -402.772)"/>
    <path id="路径_75" data-name="路径 75" class="cls-2" d="M401.409,438.276m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,400.57,438.276Z" transform="translate(-368.824 -402.772)"/>
    <path id="路径_76" data-name="路径 76" class="cls-2" d="M440.214,438.276m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,439.375,438.276Z" transform="translate(-404.554 -402.772)"/>
    <path id="路径_77" data-name="路径 77" class="cls-2" d="M475.491,438.276m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,474.652,438.276Z" transform="translate(-437.036 -402.772)"/>
    <path id="路径_78" data-name="路径 78" class="cls-2" d="M256.772,438.276m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,255.933,438.276Z" transform="translate(-235.649 -402.772)"/>
    <path id="路径_79" data-name="路径 79" class="cls-2" d="M292.048,473.554m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,291.21,473.554Z" transform="translate(-268.131 -435.253)"/>
    <path id="路径_80" data-name="路径 80" class="cls-2" d="M330.853,473.554m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,330.015,473.554Z" transform="translate(-303.861 -435.253)"/>
    <path id="路径_81" data-name="路径 81" class="cls-2" d="M366.132,473.554m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,365.293,473.554Z" transform="translate(-336.343 -435.253)"/>
    <path id="路径_82" data-name="路径 82" class="cls-2" d="M401.409,473.553m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,400.57,473.553Z" transform="translate(-368.824 -435.253)"/>
    <path id="路径_83" data-name="路径 83" class="cls-2" d="M440.214,473.554m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,439.375,473.554Z" transform="translate(-404.554 -435.253)"/>
    <path id="路径_84" data-name="路径 84" class="cls-2" d="M475.491,473.554m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,474.652,473.554Z" transform="translate(-437.036 -435.253)"/>
    <path id="路径_85" data-name="路径 85" class="cls-2" d="M256.772,473.553m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,255.933,473.553Z" transform="translate(-235.649 -435.253)"/>
    <path id="路径_86" data-name="路径 86" class="cls-2" d="M292.048,508.832m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,291.21,508.832Z" transform="translate(-268.131 -467.736)"/>
    <path id="路径_87" data-name="路径 87" class="cls-2" d="M330.853,508.832m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,330.015,508.832Z" transform="translate(-303.861 -467.736)"/>
    <path id="路径_88" data-name="路径 88" class="cls-2" d="M366.132,508.832m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,365.293,508.832Z" transform="translate(-336.343 -467.736)"/>
    <path id="路径_89" data-name="路径 89" class="cls-2" d="M401.409,508.831m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,400.57,508.831Z" transform="translate(-368.824 -467.736)"/>
    <path id="路径_90" data-name="路径 90" class="cls-2" d="M440.214,508.832m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,439.375,508.832Z" transform="translate(-404.554 -467.736)"/>
    <path id="路径_91" data-name="路径 91" class="cls-2" d="M475.491,508.832m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,474.652,508.832Z" transform="translate(-437.036 -467.736)"/>
    <path id="路径_92" data-name="路径 92" class="cls-2" d="M256.772,508.832m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,255.933,508.832Z" transform="translate(-235.649 -467.736)"/>
    <path id="路径_93" data-name="路径 93" class="cls-2" d="M292.048,544.109m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,291.21,544.109Z" transform="translate(-268.131 -500.217)"/>
    <path id="路径_94" data-name="路径 94" class="cls-2" d="M330.853,544.109m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,330.015,544.109Z" transform="translate(-303.861 -500.217)"/>
    <path id="路径_95" data-name="路径 95" class="cls-2" d="M366.132,544.109m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,365.293,544.109Z" transform="translate(-336.343 -500.217)"/>
    <path id="路径_96" data-name="路径 96" class="cls-2" d="M401.409,544.109m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,400.57,544.109Z" transform="translate(-368.824 -500.217)"/>
    <path id="路径_97" data-name="路径 97" class="cls-2" d="M440.214,544.109m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,439.375,544.109Z" transform="translate(-404.554 -500.217)"/>
    <path id="路径_98" data-name="路径 98" class="cls-2" d="M475.491,544.109m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,474.652,544.109Z" transform="translate(-437.036 -500.217)"/>
    <path id="路径_99" data-name="路径 99" class="cls-2" d="M256.772,544.109m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,255.933,544.109Z" transform="translate(-235.649 -500.217)"/>
    <path id="路径_100" data-name="路径 100" class="cls-3" d="M948.205,466.5m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,947.367,466.5Z" transform="translate(-872.289 -428.757)"/>
    <path id="路径_101" data-name="路径 101" class="cls-3" d="M987.01,466.5m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,986.172,466.5Z" transform="translate(-908.019 -428.757)"/>
    <path id="路径_102" data-name="路径 102" class="cls-3" d="M1022.285,466.5m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1021.447,466.5Z" transform="translate(-940.499 -428.757)"/>
    <path id="路径_103" data-name="路径 103" class="cls-3" d="M1057.566,466.5m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1056.727,466.5Z" transform="translate(-972.983 -428.757)"/>
    <path id="路径_104" data-name="路径 104" class="cls-3" d="M1096.365,466.5m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1095.526,466.5Z" transform="translate(-1008.708 -428.757)"/>
    <path id="路径_105" data-name="路径 105" class="cls-3" d="M1131.645,466.5m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1130.807,466.5Z" transform="translate(-1041.192 -428.757)"/>
    <path id="路径_106" data-name="路径 106" class="cls-3" d="M912.928,466.5m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,912.09,466.5Z" transform="translate(-839.808 -428.757)"/>
    <path id="路径_107" data-name="路径 107" class="cls-3" d="M948.205,501.775m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,947.367,501.775Z" transform="translate(-872.289 -461.239)"/>
    <path id="路径_108" data-name="路径 108" class="cls-3" d="M987.01,501.775m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,986.172,501.775Z" transform="translate(-908.019 -461.239)"/>
    <path id="路径_109" data-name="路径 109" class="cls-3" d="M1022.285,501.775m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1021.447,501.775Z" transform="translate(-940.499 -461.239)"/>
    <path id="路径_110" data-name="路径 110" class="cls-3" d="M1057.566,501.775m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1056.727,501.775Z" transform="translate(-972.983 -461.239)"/>
    <path id="路径_111" data-name="路径 111" class="cls-3" d="M1096.365,501.775m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1095.526,501.775Z" transform="translate(-1008.708 -461.239)"/>
    <path id="路径_112" data-name="路径 112" class="cls-3" d="M1131.645,501.775m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1130.807,501.775Z" transform="translate(-1041.192 -461.239)"/>
    <path id="路径_113" data-name="路径 113" class="cls-3" d="M912.928,501.775m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,912.09,501.775Z" transform="translate(-839.808 -461.239)"/>
    <path id="路径_114" data-name="路径 114" class="cls-3" d="M948.205,537.052m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,947.367,537.052Z" transform="translate(-872.289 -493.72)"/>
    <path id="路径_115" data-name="路径 115" class="cls-3" d="M987.01,537.052m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,986.172,537.052Z" transform="translate(-908.019 -493.72)"/>
    <path id="路径_116" data-name="路径 116" class="cls-3" d="M1022.285,537.052m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1021.447,537.052Z" transform="translate(-940.499 -493.72)"/>
    <path id="路径_117" data-name="路径 117" class="cls-3" d="M1057.566,537.052m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1056.727,537.052Z" transform="translate(-972.983 -493.72)"/>
    <path id="路径_118" data-name="路径 118" class="cls-3" d="M1096.365,537.052m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1095.526,537.052Z" transform="translate(-1008.708 -493.72)"/>
    <path id="路径_119" data-name="路径 119" class="cls-3" d="M1131.645,537.052m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1130.807,537.052Z" transform="translate(-1041.192 -493.72)"/>
    <path id="路径_120" data-name="路径 120" class="cls-3" d="M912.928,537.052m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,912.09,537.052Z" transform="translate(-839.808 -493.72)"/>
    <path id="路径_121" data-name="路径 121" class="cls-3" d="M948.205,572.33m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,947.367,572.33Z" transform="translate(-872.289 -526.203)"/>
    <path id="路径_122" data-name="路径 122" class="cls-3" d="M987.01,572.33m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,986.172,572.33Z" transform="translate(-908.019 -526.203)"/>
    <path id="路径_123" data-name="路径 123" class="cls-3" d="M1022.285,572.33m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1021.447,572.33Z" transform="translate(-940.499 -526.203)"/>
    <path id="路径_124" data-name="路径 124" class="cls-3" d="M1057.566,572.33m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1056.727,572.33Z" transform="translate(-972.983 -526.202)"/>
    <path id="路径_125" data-name="路径 125" class="cls-3" d="M1096.365,572.33m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1095.526,572.33Z" transform="translate(-1008.708 -526.203)"/>
    <path id="路径_126" data-name="路径 126" class="cls-3" d="M1131.645,572.33m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1130.807,572.33Z" transform="translate(-1041.192 -526.203)"/>
    <path id="路径_127" data-name="路径 127" class="cls-3" d="M912.928,572.33m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,912.09,572.33Z" transform="translate(-839.808 -526.203)"/>
    <path id="路径_128" data-name="路径 128" class="cls-3" d="M948.205,607.608m-.839,0a.839.839,0,1,0,.839-.839A.838.838,0,0,0,947.367,607.608Z" transform="translate(-872.289 -558.684)"/>
    <path id="路径_129" data-name="路径 129" class="cls-3" d="M987.01,607.608m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,986.172,607.608Z" transform="translate(-908.019 -558.684)"/>
    <path id="路径_130" data-name="路径 130" class="cls-3" d="M1022.285,607.608m-.839,0a.839.839,0,1,0,.839-.839A.838.838,0,0,0,1021.447,607.608Z" transform="translate(-940.499 -558.684)"/>
    <path id="路径_131" data-name="路径 131" class="cls-3" d="M1057.566,607.607m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1056.727,607.607Z" transform="translate(-972.983 -558.684)"/>
    <path id="路径_132" data-name="路径 132" class="cls-3" d="M1096.365,607.608m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1095.526,607.608Z" transform="translate(-1008.708 -558.684)"/>
    <path id="路径_133" data-name="路径 133" class="cls-3" d="M1131.645,607.608m-.839,0a.839.839,0,1,0,.839-.839A.838.838,0,0,0,1130.807,607.608Z" transform="translate(-1041.192 -558.684)"/>
    <path id="路径_134" data-name="路径 134" class="cls-3" d="M912.928,607.608m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,912.09,607.608Z" transform="translate(-839.808 -558.684)"/>
    <path id="路径_135" data-name="路径 135" class="cls-3" d="M948.205,642.884m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,947.367,642.884Z" transform="translate(-872.289 -591.166)"/>
    <path id="路径_136" data-name="路径 136" class="cls-3" d="M987.01,642.884m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,986.172,642.884Z" transform="translate(-908.019 -591.166)"/>
    <path id="路径_137" data-name="路径 137" class="cls-3" d="M1022.285,642.884m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1021.447,642.884Z" transform="translate(-940.499 -591.165)"/>
    <path id="路径_138" data-name="路径 138" class="cls-3" d="M1057.566,642.884m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1056.727,642.884Z" transform="translate(-972.983 -591.166)"/>
    <path id="路径_139" data-name="路径 139" class="cls-3" d="M1096.365,642.884m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1095.526,642.884Z" transform="translate(-1008.708 -591.166)"/>
    <path id="路径_140" data-name="路径 140" class="cls-3" d="M1131.645,642.884m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1130.807,642.884Z" transform="translate(-1041.192 -591.166)"/>
    <path id="路径_141" data-name="路径 141" class="cls-3" d="M912.928,642.884m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,912.09,642.884Z" transform="translate(-839.808 -591.166)"/>
    <path id="路径_142" data-name="路径 142" class="cls-3" d="M948.205,678.161m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,947.367,678.161Z" transform="translate(-872.289 -623.647)"/>
    <path id="路径_143" data-name="路径 143" class="cls-3" d="M987.01,678.161m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,986.172,678.161Z" transform="translate(-908.019 -623.647)"/>
    <path id="路径_144" data-name="路径 144" class="cls-3" d="M1022.285,678.161m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1021.447,678.161Z" transform="translate(-940.499 -623.647)"/>
    <path id="路径_145" data-name="路径 145" class="cls-3" d="M1057.566,678.161m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1056.727,678.161Z" transform="translate(-972.983 -623.647)"/>
    <path id="路径_146" data-name="路径 146" class="cls-3" d="M1096.365,678.161m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1095.526,678.161Z" transform="translate(-1008.708 -623.647)"/>
    <path id="路径_147" data-name="路径 147" class="cls-3" d="M1131.645,678.161m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,1130.807,678.161Z" transform="translate(-1041.192 -623.647)"/>
    <path id="路径_148" data-name="路径 148" class="cls-3" d="M912.928,678.161m-.839,0a.839.839,0,1,0,.839-.839A.839.839,0,0,0,912.09,678.161Z" transform="translate(-839.808 -623.647)"/>
    <path id="路径_149" data-name="路径 149" class="cls-4" d="M993.228,818.432m.839,0h6.709a.741.741,0,0,1,.839.839h0a.741.741,0,0,1-.839.839h-6.709a.741.741,0,0,1-.839-.839h0A.741.741,0,0,1,994.067,818.432Z" transform="translate(-914.516 -753.574)"/>
    <path id="路径_150" data-name="路径 150" class="cls-4" d="M813.314,860.765m.839,0h24.881a.741.741,0,0,1,.839.839h0a.741.741,0,0,1-.839.839H814.153a.741.741,0,0,1-.839-.839h0A.741.741,0,0,1,814.153,860.765Z" transform="translate(-748.86 -792.552)"/>
    <path id="路径_151" data-name="路径 151" class="cls-4" d="M975.589,924.264m.839,0H980.9a.741.741,0,0,1,.839.839h0a.741.741,0,0,1-.839.839h-4.473a.741.741,0,0,1-.839-.839h0A.741.741,0,0,1,976.427,924.264Z" transform="translate(-898.275 -851.018)"/>
    <path id="路径_152" data-name="路径 152" class="cls-4" d="M400.57,839.6m.839,0h4.473a.741.741,0,0,1,.839.839h0a.741.741,0,0,1-.839.839h-4.473a.741.741,0,0,1-.839-.839h0A.741.741,0,0,1,401.409,839.6Z" transform="translate(-368.825 -773.063)"/>
    <path id="路径_153" data-name="路径 153" class="cls-4" d="M375.876,903.1m.839,0h4.473a.741.741,0,0,1,.839.839h0a.741.741,0,0,1-.839.839h-4.473a.741.741,0,0,1-.839-.839h0A.741.741,0,0,1,376.715,903.1Z" transform="translate(-346.088 -831.53)"/>
    <path id="路径_154" data-name="路径 154" class="cls-4" d="M337.071,903.1m.839,0h.28a.741.741,0,0,1,.839.839h0a.741.741,0,0,1-.839.839h-.28a.741.741,0,0,1-.839-.839h0A.741.741,0,0,1,337.91,903.1Z" transform="translate(-310.358 -831.53)"/>
    <path id="路径_155" data-name="路径 155" class="cls-5" d="M880.34,860.765m.839,0h4.473a.741.741,0,0,1,.839.839h0a.741.741,0,0,1-.839.839h-4.473a.741.741,0,0,1-.839-.839h0A.741.741,0,0,1,881.179,860.765Z" transform="translate(-810.574 -792.552)"/>
    <path id="路径_156" data-name="路径 156" class="cls-6" d="M464.45,105.839m2.516,0h35.876a2.224,2.224,0,0,1,2.516,2.516v67.729a2.224,2.224,0,0,1-2.516,2.516H466.966a2.224,2.224,0,0,1-2.516-2.516V108.355A2.224,2.224,0,0,1,466.966,105.839Z" transform="translate(-427.643 -97.451)"/>
    <path id="路径_157" data-name="路径 157" class="cls-7" d="M495.109,209.078m.559,0h35.017a.494.494,0,0,1,.559.559v53.235a.494.494,0,0,1-.559.559H495.668a.494.494,0,0,1-.559-.559V209.637A.494.494,0,0,1,495.668,209.078Z" transform="translate(-455.872 -192.508)"/>
    <path id="路径_158" data-name="路径 158" class="cls-7" d="M685.154,916.756m-3.075,0a3.075,3.075,0,1,0,3.075-3.075A3.075,3.075,0,0,0,682.079,916.756Z" transform="translate(-628.025 -841.275)"/>
    <path id="路径_159" data-name="路径 159" class="cls-7" d="M653.857,166.223c0,.232.125.419.28.419s.279-.188.279-.419-.125-.419-.279-.419S653.857,165.991,653.857,166.223Z" transform="translate(-602.04 -152.664)"/>
    <path id="路径_160" data-name="路径 160" class="cls-7" d="M671.5,165.8m.419,0h5.312a.371.371,0,0,1,.419.419h0a.371.371,0,0,1-.419.419h-5.312a.371.371,0,0,1-.419-.419h0A.371.371,0,0,1,671.915,165.8Z" transform="translate(-618.28 -152.663)"/>
    <path id="路径_161" data-name="路径 161" class="cls-8" d="M593.083,454.279h-.971v7.765h.971Zm2.912,0h-1.942v7.765h1.942Zm1.941,0h-.971v7.765h.971Zm-7.765,14.56a.973.973,0,0,0,.971.971V466.9h-.971v1.941Zm.971-14.56a.973.973,0,0,0-.97.971v6.793h.971Zm13.589,0h-.971v7.765h.971Zm-3.883,0h-1.941v7.765h1.941Zm8.736.971a.974.974,0,0,0-.971-.971h-.97v7.765h1.941V455.25Zm-6.793-.971h-.972v7.765h.971Zm3.883,0H605.7v7.765h.971v-7.765Zm-.972,15.53h.971V466.9H605.7Zm1.941,0h.971a.974.974,0,0,0,.971-.971V466.9h-1.941v2.912Zm1.941-6.793H590.171a.973.973,0,0,0-.971.971v.971a.974.974,0,0,0,.971.97h19.413a.974.974,0,0,0,.971-.971v-.971a.974.974,0,0,0-.971-.971Zm0,1.941H590.171v-.972h19.413v.971Zm-5.824,4.852h.971V466.9h-.971Zm-1.941,0h.971V466.9h-.971v2.913Zm-9.707,0h.971V466.9h-.971Zm1.941,0h1.942V466.9h-1.942Zm2.912,0h.971V466.9h-.97v2.913Zm1.941,0h1.941V466.9h-1.941v2.913Z" transform="translate(-542.507 -418.278)"/>
    <path id="路径_162" data-name="路径 162" class="cls-8" d="M555.47,389.541a.682.682,0,0,1-.682-.682v-2.432a.978.978,0,0,1,.977-.977H558.2a.682.682,0,0,1,0,1.364h-2.045v2.045a.682.682,0,0,1-.682.682Z" transform="translate(-510.821 -354.904)"/>
    <path id="路径_163" data-name="路径 163" class="cls-8" d="M842.108,389.541a.682.682,0,0,0,.682-.682v-2.432a.978.978,0,0,0-.977-.977h-2.432a.682.682,0,1,0,0,1.364h2.045v2.045a.682.682,0,0,0,.682.682Z" transform="translate(-772.233 -354.904)"/>
    <path id="路径_164" data-name="路径 164" class="cls-8" d="M555.47,669.361a.682.682,0,0,0-.682.682v2.432a.978.978,0,0,0,.977.977H558.2a.682.682,0,0,0,0-1.364h-2.045v-2.045A.683.683,0,0,0,555.47,669.361Z" transform="translate(-510.821 -616.315)"/>
    <path id="路径_165" data-name="路径 165" class="cls-8" d="M842.108,669.361a.682.682,0,0,1,.682.682v2.432a.978.978,0,0,1-.977.977h-2.432a.682.682,0,1,1,0-1.364h2.045v-2.045A.682.682,0,0,1,842.108,669.361Z" transform="translate(-772.233 -616.315)"/>
    <path id="路径_166" data-name="路径 166" class="cls-2" d="M725.12,0m.559,0h0a.494.494,0,0,1,.559.559v2.8a.494.494,0,0,1-.559.559h0a.494.494,0,0,1-.559-.559V.559A.494.494,0,0,1,725.679,0Z" transform="translate(-667.655)"/>
    <path id="路径_167" data-name="路径 167" class="cls-2" d="M774.508,3.527m.507.236h0a.494.494,0,0,1,.27.743L774.1,7.04a.494.494,0,0,1-.743.27h0a.494.494,0,0,1-.271-.743l1.181-2.534A.494.494,0,0,1,775.015,3.763Z" transform="translate(-711.755 -3.247)"/>
    <path id="路径_168" data-name="路径 168" class="cls-2" d="M662.638,8.943m-.507.236h0a.494.494,0,0,1-.743-.27l-1.182-2.534a.494.494,0,0,1,.271-.743h0a.494.494,0,0,1,.743.27L662.4,8.436A.494.494,0,0,1,662.131,9.179Z" transform="translate(-607.817 -5.116)"/>
  </g>
</svg>
