<svg id="选中2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="14" height="16" viewBox="0 0 14 16">
  <defs>
    <style>
      .cls-1 {
        fill: rgba(255,255,255,0.01);
      }

      .cls-2 {
        clip-path: url(#clip-path);
      }

      .cls-3 {
        fill: #458bf7;
      }
    </style>
    <clipPath id="clip-path">
      <rect id="Rectangle" class="cls-1" width="14" height="14"/>
    </clipPath>
  </defs>
  <rect id="Rectangle-2" data-name="Rectangle" class="cls-1" width="14" height="14" transform="translate(0 2)"/>
  <g id="选中">
    <rect id="Rectangle-3" data-name="Rectangle" class="cls-1" width="14" height="14"/>
    <g id="选中-2" data-name="选中" class="cls-2">
      <path id="Path" class="cls-3" d="M12.642.156,13.864,1.45a.5.5,0,0,1-.02.706l0,0-8.353,7.7a.5.5,0,0,1-.7-.022L.138,4.974a.5.5,0,0,1,.016-.706l0,0,1.3-1.215a.5.5,0,0,1,.7.02L4.912,5.947a.5.5,0,0,0,.7.022L11.941.132a.5.5,0,0,1,.7.024Z" transform="translate(0 2)"/>
    </g>
  </g>
</svg>
