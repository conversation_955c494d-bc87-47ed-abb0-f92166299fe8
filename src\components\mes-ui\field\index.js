import { Input } from "@hips/vue-ui";
import { Store } from "@hips/vue-core";
import { isEmpty } from "lodash";
import IconImg from "../icon/index.vue";
import InputRightButton from "../input-right-button/index.vue";
import "./index.styl";

import { confirmExit, mapLanguageGetters } from "@/utils";

const { mapState } = Store;
const modelPrompt = "tarzan.pda.common.component.scanField";

export default {
  name: "field",
  components: {
    [Input.name]: Input,
    [IconImg.name]: IconImg,
    [InputRightButton.name]: InputRightButton,
  },
  props: {
    clear: Function,
    required: <PERSON><PERSON><PERSON>,
    disabled: Boolean,
    inputValue: [String, Array, Object],
    iconProps: {
      type: [Object, Boolean],
      default: () => ({
        name: "add-container",
      }),
    },
    rightIconOptions: Array,
    label: {
      type: String,
      default: undefined,
    },
  },

  computed: {
    ...mapState("loadingStore", {
      globalLoading: (state) => state.global,
    }),
    ...mapLanguageGetters(modelPrompt),
    currentDisabled() {
      if (
        this.rightIconOptions &&
        this.rightIconOptions.length > 0 &&
        this.rightIconOptions.some((e) => e.iconName === "lock")
      ) {
        const lockFlag = this.rightIconOptions.find(
          (e) => e.iconName === "lock"
        )?.lockFlag;
        return lockFlag || this.disabled;
      }
      return this.disabled;
    },
    currentIconName() {
      if (this.iconProps?.name) {
        return this.iconProps.name;
      }
      return "add-container";
    },
  },

  methods: {
    exit() {
      confirmExit();
    },

    // 查询框
    handleEnter() {
      this.childValue = this.childValue?.trim();
      this.$emit("enter", this.childValue);
    },

    // 选择按钮逻辑
    handleClearInputValue() {
      this.$emit("clear", "");
    },

    clickImg() {
      this.$emit("click");
    },
  },

  render() {
    return (
      <div
        class={`pda-input-row ${
          this.label ? "pda-input-row-top-border" : ""
        } mes-ui-field`}
      >
        {!this.label ? (
          <div
            class={`pda-input-row-icon ${
              this.required ? "label-required" : ""
            }`}
          >
            {this.iconProps ? (
              this.iconProps?.type === "icon" ? (
                <iconImg
                  name={this.iconProps?.name}
                  type={this.iconProps?.type}
                  color={this.iconProps?.color}
                  className="label-icon"
                  vOn:click={this.clickImg}
                />
              ) : (
                <iconImg
                  name={`${
                    this.required
                      ? `${this.iconProps?.name}-o`
                      : this.iconProps?.name
                  }`}
                  className="label-icon"
                  vOn:click={this.clickImg}
                />
              )
            ) : null}
          </div>
        ) : (
          <div class={`pda-input-row-input-label`}>
            <div
              class={`pda-input-row-label ${
                this.required ? "label-required" : ""
              }`}
              style={{ marginRight: "10px" }}
            >
              {this.label}
            </div>
          </div>
        )}

        <div
          class={`pda-input-row-input ${
            this.label ? "pda-input-row-input-right-algin" : ""
          }`}
        >
          {this.$slots.field}
        </div>
        <div class="pda-input-row-icon">
          {!this.currentDisabled && (
            <iconImg
              name="icon-close"
              style={{ marginRight: "5px" }}
              vShow={!isEmpty(this.inputValue)}
              vOn:click={this.handleClearInputValue}
              className=""
            />
          )}
          {this.rightIconOptions &&
            this.rightIconOptions.length > 0 &&
            this.rightIconOptions.map((e) => <inputRightButton options={e} />)}
        </div>
      </div>
    );
  },
};
