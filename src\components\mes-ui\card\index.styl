@import "../../../style/pda-var.styl"

.mes-ui-card {
  .pda-tag-group {
    top: 16px;
    z-index: 1;
    right: 16px;
  }

  .pda-tag-group.pda-tag-group-title {
    top: 5px;
  }

  .pda-expand-group .pda-expand-detail .pda-expand-detail-title .pda-expand-detail-title-item.title-item-right {
    right: -4px;
    z-index: 100;
  }

  .number-manage{
    width:80px;
    position: absolute;
    top: 30px;
    right: 0;
  }

  .pda-expand-detail-row {
    width: 100%;
    align-items: baseline;
  }

  .pda-expand-detail-row.half{
    width: 50%;
    .pda-expand-detail-row-item{
      &.row-item-left{
        width: 60px;
      }
    }
  }

  .pda-expand-detail-content {
    width: 100%;
    height: auto;
    display: flex;
    flex-wrap: wrap;
  }

  .pda-expand-group .pda-expand-detail .pda-expand-detail-row {
    .link {
      color: $color-primary;
      .pda-expand-detail-row-item.row-item-left {
        color: $color-primary;
      }
      .pda-expand-detail-row-item.row-item-right {
        color: $color-primary;
      }
    }
    .pda-expand-detail-row-item.row-item-left.link {
      color: $color-primary;
    }
    .pda-expand-detail-row-item.row-item-right.link {
      color: $color-primary;
    }
  }

  .pda-expand-detail-select{
    .hips-checkbox{
      padding: 0;
    }
    .hips-checkbox__wrap {
      padding: 0;
    }
    .hips-checkbox-group {
      background-color: transparent;
      overflow: visible;
    }
    .hips-checkbox__ui {
      width: 16px;
      height: 16px;
      border-color: #ccc;
    }
  }

  .pda-expand-group .pda-expand-detail .pda-expand-detail-row .pda-expand-detail-row-item.row-item-left {
    width: 16vw;
  }

  .pda-expand-group .pda-expand-detail .pda-expand-detail-title .pda-expand-detail-title-item.title-item-left.mes-ui-card-title {
    width: 68% !important;
    .font-bolder {
      word-break: break-all !important;
    }
  }
  .pda-expand-group .pda-expand-detail .pda-expand-detail-row .pda-expand-detail-row-item.mes-ui-card-subTitleField {
    width: 90% !important;
  }
}

.mes-ui-card.pda-card-data-area {
  background: #fff;
  .hips-tabs--line .hips-tabs__wrapper {
    height: auto;
    .hips-tab--active {
      font-size: 13px;
    }
  }
  .hips-tab, .hips-tab__panel {
    font-size: 12px;
  }
  .hips-tab {
    line-height: 36px;
  }
  .hips-tab__panel {
    padding: 0;
  }
}

.mes-ui-card.pda-card:last-of-type{
  margin-bottom: 0;
}