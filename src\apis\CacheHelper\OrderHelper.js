/**
 * @Description: 单据-条码缓存
 * @Author: <<EMAIL>>
 * @Date: 2021-12-29 14:20:21
 * @LastEditTime: 2021-12-29 18:45:49
 * @LastEditors: <<EMAIL>>
 */

import { getTenantId, getBasicConfig } from "@/utils";
import http from "@/request";

const BasicConfig = getBasicConfig();
const tenantId = getTenantId();

const OrderHelper = {
  // params: string
  async validateOrderBarcode(params) {
    const url = `${BasicConfig}/v1/${tenantId}/mt-scan-cache/barcode/existence/verify`;
    return http.post(url, params);
  },

  // params: string[]
  async validateOrderBarcodes(params) {
    const url = `${BasicConfig}/v1/${tenantId}/mt-scan-cache/barcode/existence/batch/verify`;
    return http.post(url, params);
  },

  // {
  // barcodes: any[]，
  // operationType: string，
  // docNum: string
  // }
  async saveOrderBarcodes(params) {
    const url = `${BasicConfig}/v1/${tenantId}/mt-scan-cache/order-barcodes/cache`;
    return http.post(url, params);
  },

  // {
  //   barcodes: string[],
  //   operationType: string，
  //   docNum: string
  // }
  async delOrderBarcodes(params) {
    const url = `${BasicConfig}/v1/${tenantId}/mt-scan-cache/order-barcodes/cache/remove`;
    return http.post(url, params);
  },

  // {
  //   operationType: string，
  //   docNum: string
  // }
  async delOrder(params) {
    const url = `${BasicConfig}/v1/${tenantId}/mt-scan-cache/order/cache/remove`;
    return http.post(url, params);
  },

  // {
  //   operationType: string，
  //   docNum: string
  // }
  async getOrderCache(params) {
    const url = `${BasicConfig}/v1/${tenantId}/mt-scan-cache/order/cache/get?operationType=${params.operationType}&docNum=${params.docNum}`;
    return http.get(url);
  },
};

export default OrderHelper;
