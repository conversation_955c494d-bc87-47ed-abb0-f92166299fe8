"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

Object.defineProperty(exports, "__esModule", {
  value: true,
});
exports["default"] = void 0;

var _create = _interopRequireDefault(require("@hips/vue-ui/lib/utils/create"));

var _picker = _interopRequireDefault(require("@hips/vue-ui/lib/picker"));

var _utils = require("@hips/vue-ui/lib/utils");

var _default2 = (0, _create["default"])({
  render: function render() {
    var _vm = this;

    var _h = _vm.$createElement;

    var _c = _vm._self._c || _h;

    return _c(
      "picker",
      {
        ref: "hipsPicker",
        attrs: {
          data: _vm.pickers,
          "selected-index": _vm.pickersIndex,
          title: _vm.title,
          "cancel-text": _vm.cancelText,
          "confirm-text": _vm.confirmText,
          "show-toolbar": "",
        },
        on: {
          confirm: _vm._handleConfirm,
          cancel: _vm._handleCancel,
          change: _vm._handleChange,
          "custom-title": _vm._handleCustomTitle,
        },
      },
      [
        _vm._t("title", null, {
          slot: "title",
        }),
      ],
      2
    );
  },
  name: "datetime-picker",
  components: {
    Picker: _picker["default"],
  },
  props: {
    // 取消按钮的值
    cancelText: {
      type: String,
      default: "取消",
    },
    // 确认按钮的值
    confirmText: {
      type: String,
      default: "确认",
    },
    // 居中的标题
    title: {
      type: String,
      default: "",
    },
    // 最小年份
    minYear: Number,
    // 最大年份
    maxYear: Number,
    // 开始日期
    startDate: String,
    // 结束日期
    endDate: String,
    // 转化格式
    format: {
      type: String,
      default: "yyyy-MM-dd",
      validator: function validator(val) {
        return [
          "yyyy",
          "yyyy-MM",
          "yyyy-MM-dd",
          "HH:mm",
          "HH:mm:ss",
          "yyyy-MM-dd HH:mm",
          "yyyy-MM-dd HH:mm:ss",
        ].includes(val.replace(/\//g, "-"));
      },
    },
    // 设置单位
    unit: {
      type: Boolean,
      default: true,
    },
    // 设置默认选中日期
    defaultSelectedValue: {
      type: String,
    },
    // 自定义单位
    localeUnit: {
      type: Object,
      default: function _default() {
        return {};
      },
    },
  },
  data: function data() {
    var now = new _utils.PickerDate({
      format: this.format,
    });
    var initValue = now.getValue();
    var pickerWheelIndex = this.format
      .replace(" ", "-")
      .replace(/:/g, "-")
      .replace(/\//g, "-")
      .split("-");
    return {
      // 用于picker选择的数据，非级联
      now: now,
      pickers: [],
      // 存储选中的index
      pickersIndex: [],
      // 保存每次变更后的数据
      year: "",
      month: "",
      date: "",
      hour: "",
      minute: "",
      second: "",
      locale: {},
      // 默认时间是当前时间
      value: initValue,
      pickerWheelIndex: pickerWheelIndex,
      currentMaxYear: "",
      currentMaxMonth: "",
      currentMaxDay: "",
      currentMaxHour: "",
      currentMaxMinute: "",
      currentMaxSeconds: "",
    };
  },
  mounted: function mounted() {
    this._handleLan(); // 处理单位 和 初始化this.year 等
  },
  watch: {
    year: function year(val) {
      if (!val) return;
      this.$emit("change", {
        year: val,
        month: this.month,
        date: this.date,
        hour: this.hour,
        minute: this.minute,
        second: this.second,
      });
      if (!this.format.includes("yyyy")) return;

      var months = this._getMonths();

      if (
        this.pickers[1] &&
        this.pickers[1].length === months.length &&
        !this.startDate &&
        !this.endDate
      ) {
        return;
      }

      this.parseIndex();
      this.$refs.hipsPicker.setPickerData(this.pickers, this.pickersIndex);
      this.parseMonth(); // 年改变时当前月是2月份，需要做单独的天数数据计算

      if (String(this.month) === "2") {
        this.parseDate();
      }
    },
    month: function month(val) {
      if (!val) return;
      this.$emit("change", {
        year: this.year,
        month: val,
        date: this.date,
        hour: this.hour,
        minute: this.minute,
        second: this.second,
      });
      if (!this.format.includes("MM")) return;
      var dates = this._getDates(); // 如果change之前天数和change之后天数相同，不重新渲染数据   如果存在开始日期和结束日期限制 则需重新判断

      if (
        this.pickers[2] &&
        this.pickers[2].length === dates.length &&
        !this.startDate &&
        !this.endDate
      ) {
        return;
      }
      this.parseIndex();

      this.$refs.hipsPicker.setPickerData(this.pickers, this.pickersIndex);
      this.parseDate();
    },
    date: function date(val) {
      if (!val) return;
      this.$emit("change", {
        year: this.year,
        month: this.month,
        date: val,
        hour: this.hour,
        minute: this.minute,
        second: this.second,
      });
      if (!this.format.includes("dd")) return;

      var hours = this._getHours(); // 如果change之前小时数和change之后小时数相同，不重新渲染数据   如果存在开始日期和结束日期限制 则需重新判断

      if (
        this.pickers[3] &&
        this.pickers[3].length === hours.length &&
        !this.startDate &&
        !this.endDate
      ) {
        return;
      }

      this.parseIndex();
      this.$refs.hipsPicker.setPickerData(this.pickers, this.pickersIndex);
      this.parseHour();
    },
    hour: function hour(val) {
      if (!val) return;
      this.$emit("change", {
        year: this.year,
        month: this.month,
        date: this.date,
        hour: val,
        minute: this.minute,
        second: this.second,
      });
      if (!this.format.includes("HH")) return;

      var minutes = this._getMinutes(); // 如果change之前小时数和change之后小时数相同，不重新渲染数据   如果存在开始日期和结束日期限制 则需重新判断

      if (
        this.pickers[4] &&
        this.pickers[4].length === minutes.length &&
        !this.startDate &&
        !this.endDate
      ) {
        return;
      }

      this.parseIndex();
      this.$refs.hipsPicker.setPickerData(this.pickers, this.pickersIndex);
      this.parseMinute();
    },
    minute: function minute(val) {
      if (!val) return;
      this.$emit("change", {
        year: this.year,
        month: this.month,
        date: this.date,
        hour: this.hour,
        minute: val,
        second: this.second,
      });
      if (!this.format.includes("mm")) return;

      var seconds = this._getSeconds(); // 如果change之前小时数和change之后小时数相同，不重新渲染数据  如果存在开始日期和结束日期限制 则需重新判断
      if (
        this.pickers[5] &&
        this.pickers[5].length === seconds.length &&
        !this.startDate &&
        !this.endDate
      ) {
        return;
      }

      this.parseIndex();
      this.$refs.hipsPicker.setPickerData(this.pickers, this.pickersIndex);
      this.parseSecond();
    },
    second: function second(val) {
      if (!val) return;
      this.$emit("change", {
        year: this.year,
        month: month,
        date: this.date,
        hour: this.hour,
        minute: val,
        second: this.second,
      });
      if (!this.format.includes("ss")) return;
      this.parseIndex();
      this.$refs.hipsPicker.setPickerData(this.pickers, this.pickersIndex);
      this.parseSecond();
    },
    defaultSelectedValue: function defaultSelectedValue(newVal) {
      this.initial();
      this.refresh();
    },
    startDate: function startDate(newVal) {
      this.initial();
      this.refresh();
    },
    endDate: function endDate(newVal) {
      this.initial();
      this.refresh();
    },
  },
  methods: {
    // 初始化数据
    initial: function initial() {
      this.dealDateFormat(); // 初始化 selectValue 和 startDate && endDate

      this.parseValue();
      this.parseIndex();
    },
    // 手动刷新
    refresh: function refresh() {
      var _this = this;

      this.$nextTick(function () {
        _this.$refs.hipsPicker.refresh();
      });
    },
    // 处理 start-date | end-date | default-selected-value 的场景
    dealDateFormat: function dealDateFormat() {
      // defaultSelectedValue 需满足时间格式要求
      if (
        this.defaultSelectedValue &&
        (0, _utils.dateFormat)(this.defaultSelectedValue, this.format)
      ) {
        // 设置了 defaultSelectedValue 就取代默认时间了 需要在最大时间和最小时间之间 否则取最小时间
        this.now = new _utils.PickerDate({
          date: this.defaultSelectedValue.replace(/-/g, "/"),
          format: this.format,
        });
        this.value = this.now.getValue();
      } // 是否有最小时间限制

      if (
        this.startDate &&
        (0, _utils.dateFormat)(this.startDate, this.format)
      ) {
        var minDate = new _utils.PickerDate({
          date: this.startDate.replace(/-/g, "/"),
          format: this.format,
        });
        this.currentMinYear = minDate.getFullYear();
        this.currentMinMonth = minDate.getMonth();
        this.currentMinDay = minDate.getDate();
        this.currentMinHour = minDate.getHours();
        this.currentMinMinute = minDate.getMinutes();
        this.currentMinSeconds = minDate.getSeconds(); // 设置的最小时间大于默认当前时间  则取最小时间

        if (minDate.getTime() > this.now.getTime()) {
          this.value = minDate.getValue();
        }
      }

      if (this.endDate && (0, _utils.dateFormat)(this.endDate, this.format)) {
        var maxDate = new _utils.PickerDate({
          date: this.endDate.replace(/-/g, "/"),
          format: this.format,
        });
        this.currentMaxYear = maxDate.getFullYear();
        this.currentMaxMonth = maxDate.getMonth();
        this.currentMaxDay = maxDate.getDate();
        this.currentMaxHour = maxDate.getHours();
        this.currentMaxMinute = maxDate.getMinutes();
        this.currentMaxSeconds = maxDate.getSeconds(); // 设置的最大时间小于默认当前时间  则取最大时间

        if (maxDate.getTime() < this.now.getTime()) {
          this.value = maxDate.getValue();
        }
      } // 清除状态

      if (!this.endDate) {
        this.currentMaxYear = null;
        this.currentMaxMonth = null;
        this.currentMaxDay = null;
        this.currentMaxHour = null;
        this.currentMaxMinute = null;
        this.currentMaxSeconds = null;
      }

      if (!this.startDate) {
        this.currentMinYear = null;
        this.currentMinMonth = null;
        this.currentMinDay = null;
        this.currentMinHour = null;
        this.currentMinMinute = null;
        this.currentMinSeconds = null;
      }
    },
    // 处理语言导致的单位
    _handleLan: function _handleLan() {
      // 都为true，中文带单位的
      if (this.unit) {
        this.locale = {
          year: "年",
          month: "月",
          date: "日",
          hour: "时",
          minute: "分",
          second: "秒",
        };
      } else if (!this.unit) {
        // 中文或英文不带单位
        this.locale = {
          year: "",
          month: "",
          date: "",
          hour: "",
          minute: "",
          second: "",
        };
      } // 合并自定义 单位
      // this.locale = {
      //   ...this.localeUnit
      // }

      this.initial();
    },
    // 处理当前的value，得到year，month等值
    parseValue: function parseValue(value) {
      if (value === void 0) {
        value = this.value;
      }

      // if (!/[-\\/]/.test(value) && this.format !== 'yyyy') {
      //   value = '1900/01/0' + value
      // }
      var date = new _utils.PickerDate({
        date: value.replace(/-/g, "/"),
        format: this.format,
      }); // 初始化year和month

      this.year = date.getFullYear();
      this.month = date.getMonth();
      this.date = date.getDate();
      this.hour = date.getHours();
      this.minute = date.getMinutes();
      this.second = date.getSeconds();
    },
    // 获取每个数组的index和数组
    parseIndex: function parseIndex() {
      this.pickers = [];
      this.pickersIndex = [];

      var years = this._getYears();

      var months = this._getMonths();

      var dates = this._getDates();

      var hours = this._getHours();

      var minutes = this._getMinutes();

      var seconds = this._getSeconds();

      this.dealPickers(years, months, dates, hours, minutes, seconds);
    },
    // 数据拆分处理
    dealPickers: function dealPickers(
      years,
      months,
      dates,
      hours,
      minutes,
      seconds
    ) {
      if (this.judgeFormat("yyyy")) {
        this.pickers.push(years);
        this.dealIndex(years, +this.year);
      }

      if (this.judgeFormat("MM")) {
        this.pickers.push(months); // eslint-disable-next-line

        if (
          this.year &&
          this.year === this.currentMinYear &&
          this.month &&
          +this.month < +this.currentMinMonth
        ) {
          this.month = this.currentMinMonth;
        } // eslint-disable-next-line

        if (
          this.year &&
          this.year === this.currentMaxYear &&
          this.month &&
          +this.month > +this.currentMaxMonth
        ) {
          this.month = this.currentMaxMonth;
        }

        this.dealIndex(months, +this.month);
      }

      if (this.judgeFormat("dd")) {
        this.pickers.push(dates); // eslint-disable-next-line

        if (
          this.year &&
          this.year === this.currentMinYear &&
          this.month &&
          this.month === this.currentMinMonth &&
          this.date &&
          +this.date < +this.currentMinDay
        ) {
          this.date = this.currentMinDay;
        } // eslint-disable-next-line

        if (
          this.year &&
          this.year === this.currentMaxYear &&
          this.month &&
          this.month === this.currentMaxMonth &&
          this.date &&
          +this.date > +this.currentMaxDay
        ) {
          this.date = this.currentMaxDay;
        }

        this.dealIndex(dates, +this.date);
      }

      if (this.judgeFormat("HH")) {
        this.pickers.push(hours);
        this.dealIndex(hours, +this.hour);
      }

      if (this.judgeFormat("mm")) {
        this.pickers.push(minutes);
        this.dealIndex(minutes, +this.minute);
      }

      if (this.judgeFormat("ss")) {
        this.pickers.push(seconds);
        this.dealIndex(seconds, +this.second);
      }
    },
    // 获取每个应有的index
    dealIndex: function dealIndex(data, type) {
      var _this2 = this;

      data.forEach(function (item, index) {
        if (+item.code === type) {
          _this2.pickersIndex.push(index);
        }
      });
    },
    // 判断字符串是否存在(判读的字符串，字符串)
    judgeFormat: function judgeFormat(type, data) {
      if (data) {
        return data.indexOf(type) > -1;
      } else {
        return this.format.indexOf(type) > -1;
      }
    },
    parseMonth: function parseMonth() {
      var _this3 = this;

      var months = this._getMonths();

      if (this.judgeFormat("MM")) {
        // 用于判断当前日是否在新的dates数组中，不在就需要往上移date
        var has = months.some(function (item) {
          return item.code === +_this3.month;
        });
        this.pickers[1] = months;

        if (!has) {
          this.month = months[months.length - 1].code;
          this.pickersIndex[1] = months.length - 1;
        }

        this.$refs.hipsPicker.setPickerData(this.pickers, this.pickersIndex);
      } else {
        // 触发天数变动时，如果format不需要天数，则将天数处理为当前
        this.month = months.length ? months[0].code : "";
      }
    },
    // 单独处理因为年月变动而引起的天数数组变动 (新增处理 年月日时分 change时 重新判断)
    parseDate: function parseDate() {
      var _this4 = this;

      var dates = this._getDates();

      if (this.judgeFormat("dd")) {
        // 用于判断当前日是否在新的dates数组中，不在就需要往上移date
        var has = dates.some(function (item) {
          return item.code === +_this4.date;
        });
        this.pickers[2] = dates;

        if (!has) {
          this.date = dates[dates.length - 1].code;
          this.pickersIndex[2] = dates.length - 1;
        }

        this.$refs.hipsPicker.setPickerData(this.pickers, this.pickersIndex);
      } else {
        // 触发天数变动时，如果format不需要天数，则将天数处理为当前
        this.date = dates.length ? dates[0].code : "";
      }
    },
    // 处理小时变化
    parseHour: function parseHour() {
      var _this5 = this;

      // 有开始结束时间 需判断时分秒的变化
      if (this.startDate || this.endDate) {
        var hours = this._getHours();

        if (this.judgeFormat("HH")) {
          // 用于判断当前日是否在新的dates数组中，不在就需要往上移date
          var has = hours.some(function (item) {
            return item.code === String(_this5.hour);
          });
          this.pickers[3] = hours;

          if (!has) {
            this.hour = hours[hours.length - 1].code;
            this.pickersIndex[3] = hours.length - 1;
          }

          this.$refs.hipsPicker.setPickerData(this.pickers, this.pickersIndex);
        } else {
          this.hour = hours.length ? hours[0].code : "";
        }
      }
    },
    parseMinute: function parseMinute() {
      var _this6 = this;

      // 有开始结束时间 需判断时分秒的变化
      if (this.startDate || this.endDate) {
        var minutes = this._getMinutes();

        if (this.judgeFormat("mm")) {
          // 用于判断当前日是否在新的dates数组中，不在就需要往上移date
          var has = minutes.some(function (item) {
            return item.code === String(_this6.minute);
          });
          this.pickers[4] = minutes;

          if (!has) {
            this.minute = minutes[minutes.length - 1].code;
            this.pickersIndex[4] = minutes.length - 1;
          }

          this.$refs.hipsPicker.setPickerData(this.pickers, this.pickersIndex);
        } else {
          this.minute = minutes.length ? minutes[0].code : "";
        }
      }
    },
    parseSecond: function parseSecond() {
      var _this7 = this;

      // 有开始结束时间 需判断时分秒的变化
      if (this.startDate || this.endDate) {
        var seconds = this._getSeconds();

        if (this.judgeFormat("ss")) {
          // 用于判断当前日是否在新的dates数组中，不在就需要往上移date
          var has = seconds.some(function (item) {
            return item.code === String(_this7.second);
          });
          this.pickers[5] = seconds;

          if (!has) {
            this.second = seconds[seconds.length - 1].code;
            this.pickersIndex[5] = seconds.length - 1;
          }

          this.$refs.hipsPicker.setPickerData(this.pickers, this.pickersIndex);
        } else {
          this.second = seconds.length ? seconds[0].code : "";
        }
      }
    },
    // 数据数组统一处理（开始循环，结束循环，默认单位，固定单位以区分类型）
    _handleData: function _handleData(start, end, unit) {
      var data = [];

      for (var i = +start; i <= +end; i++) {
        data.push({
          value: (i < 10 ? "0" + i : String(i)) + unit,
          code: i,
        });
      }

      return data;
    },
    // 获取年的数组(需判断当前year是否在默认/设置的最大最小年之间)
    _getYears: function _getYears() {
      // 当前value绑定的年
      var val = this.year; // 是否有配置最小年份和最大年份， 无则默认是与当前十年

      var minYear = this.minYear
        ? this.minYear
        : "" + (+this.now.getFullYear() - 10);
      var maxYear = this.maxYear
        ? this.maxYear
        : "" + (+this.now.getFullYear() + 10); // 如果 有配置 startDate 和 endDate, 进入以下逻辑

      if (this.currentMinYear || this.currentMaxYear) {
        // 只配置了startDate
        if (this.currentMinYear) {
          minYear = this.currentMinYear; // maxYear = +this.currentMinYear + 10
        } // 只配置了endDate

        if (this.currentMaxYear) {
          // minYear = +this.currentMaxYear - 10
          maxYear = this.currentMaxYear;
        } // startDate 和 endDate 都配置了
        // if (this.currentMinYear && this.currentMaxYear) {
        //   minYear = this.currentMinYear
        //   maxYear = this.currentMaxYear
        // }
      } else {
        // 未设置startDate和endDate 只单独设置了 defaultSelectedValue 取默认十年
        if (this.defaultSelectedValue) {
          var newDate = new _utils.PickerDate({
            date: this.defaultSelectedValue.replace(/-/g, "/"),
            format: this.format,
          });
          var newYear = newDate.getFullYear();
          minYear = minYear <= +newYear ? minYear : +newYear;
          maxYear = maxYear <= +newYear ? +newYear : maxYear;
        } else {
          minYear = minYear <= +val ? minYear : +val;
          maxYear = maxYear <= +val ? +val : maxYear;
        }
      }

      return this._handleData(minYear, maxYear, this.locale.year);
    },
    // 获取月份数组
    _getMonths: function _getMonths() {
      var _ref = {},
        _ref$minMonth = _ref.minMonth,
        minMonth = _ref$minMonth === void 0 ? 1 : _ref$minMonth,
        _ref$maxMonth = _ref.maxMonth,
        maxMonth = _ref$maxMonth === void 0 ? 12 : _ref$maxMonth;

      if (this.year === this.currentMinYear) {
        minMonth = this.currentMinMonth;
        maxMonth = 12;
      } else if (this.year === this.currentMaxYear) {
        minMonth = 1;
        maxMonth = this.currentMaxMonth;
      }

      if (
        this.currentMinYear === this.currentMaxYear &&
        this.year === this.currentMinYear
      ) {
        minMonth = this.currentMinMonth ? this.currentMinMonth : 1;
        maxMonth = this.currentMaxMonth ? this.currentMaxMonth : 12;
      }

      return this._handleData(minMonth, maxMonth, this.locale.month);
    },
    // 计算该年该月应该有多少天
    _getDates: function _getDates() {
      var year = this.year;
      var month = this.month;
      var date = new Date(parseInt(year, 10), parseInt(month, 10), 0); // 该年该月有多少天

      var _ref2 = {},
        minDay = _ref2.minDay,
        maxDay = _ref2.maxDay; // eslint-disable-next-line

      if (
        this.year === this.currentMinYear &&
        this.month === this.currentMinMonth
      ) {
        minDay = this.currentMinDay ? this.currentMinDay : 1;
        maxDay = date.getDate(); // eslint-disable-next-line
      } else if (
        this.year === this.currentMaxYear &&
        this.month === this.currentMaxMonth
      ) {
        minDay = 1;
        maxDay = this.currentMaxDay;
      } else {
        minDay = 1;
        maxDay = date.getDate();
      } // 单独处理  当月份相同时
      // eslint-disable-next-line

      if (
        this.currentMinYear === this.currentMaxYear &&
        this.currentMinMonth === this.currentMaxMonth
      ) {
        minDay = this.currentMinDay ? this.currentMinDay : 1;
        maxDay = this.currentMaxDay ? this.currentMaxDay : date.getDate();
      } // 如果change之前天数和change之后天数相同，不重新渲染数据

      if (this.pickers[2] && this.pickers[2].length === date.getDate()) {
        return this.pickers[2];
      }

      return this._handleData(minDay, maxDay, this.locale.date);
    },
    // 获取小时（固定）
    _getHours: function _getHours() {
      var _ref3 = {},
        _ref3$minHour = _ref3.minHour,
        minHour = _ref3$minHour === void 0 ? 0 : _ref3$minHour,
        _ref3$maxHour = _ref3.maxHour,
        maxHour = _ref3$maxHour === void 0 ? 23 : _ref3$maxHour;

      if (this.endDate || this.startDate) {
        var year = this.year;
        var month = this.month;
        var date = this.date; // eslint-disable-next-line

        if (
          year === this.currentMinYear &&
          month === this.currentMinMonth &&
          date === this.currentMinDay
        ) {
          // 当前年月日等于最小年月日
          minHour = this.currentMinHour ? this.currentMinHour : 0;
          maxHour = 23; // eslint-disable-next-line
        } else if (
          year === this.currentMaxYear &&
          month === this.currentMaxMonth &&
          date === this.currentMaxDay
        ) {
          // 当前年月日等于最小年月日
          minHour = 0;
          maxHour = this.currentMaxHour ? this.currentMaxHour : 23;
        } // eslint-disable-next-line

        if (
          this.currentMinYear === this.currentMaxYear &&
          this.currentMinMonth === this.currentMaxMonth &&
          this.currentMinDay === this.currentMaxDay
        ) {
          // 单独处理 当前最大年月日等于当前最小年月日
          minHour = this.currentMinHour ? this.currentMinHour : 0;
          maxHour = this.currentMaxHour ? this.currentMaxHour : 23;
        }
      } // 如果change之前小时数和change之后小时数据相同，不重新渲染数据

      if (this.pickers[3] && this.pickers[3].length === maxHour - minHour + 1) {
        return this.pickers[3];
      } // 判断是否有currentMinHour和currentMaxHour

      return this._handleData(minHour, maxHour, this.locale.hour);
    },
    // 获取分钟（固定）
    _getMinutes: function _getMinutes() {
      var _ref4 = {},
        _ref4$minMinute = _ref4.minMinute,
        minMinute = _ref4$minMinute === void 0 ? 0 : _ref4$minMinute,
        _ref4$maxMinute = _ref4.maxMinute,
        maxMinute = _ref4$maxMinute === void 0 ? 59 : _ref4$maxMinute; // 有开始结束时间才进行判断

      if (this.startDate || this.endDate) {
        var year = this.year;
        var month = this.month;
        var date = this.date;
        var hour = this.hour; // eslint-disable-next-line

        if (
          year === this.currentMinYear &&
          month === this.currentMinMonth &&
          date === this.currentMinDay &&
          hour === this.currentMinHour
        ) {
          // 当前年月日时等于最小年月日时
          minMinute = this.currentMinMinute ? this.currentMinMinute : 0;
          maxMinute = 59; // eslint-disable-next-line
        } else if (
          year === this.currentMaxYear &&
          month === this.currentMaxMonth &&
          date === this.currentMaxDay &&
          hour === this.currentMaxHour
        ) {
          // 当前年月日时等于最小年月日时
          minMinute = 0;
          maxMinute = this.currentMaxMinute ? this.currentMaxMinute : 59;
        } // eslint-disable-next-line

        if (
          this.currentMinYear === this.currentMaxYear &&
          this.currentMinMonth === this.currentMaxMonth &&
          this.currentMinDay === this.currentMaxDay &&
          this.currentMinHour === this.currentMaxHour
        ) {
          // 单独处理 当前最大年月日时等于当前最小年月日时
          minMinute = this.currentMinMinute ? this.currentMinMinute : 0;
          maxMinute = this.currentMaxMinute ? this.currentMaxMinute : 59;
        }
      } // 如果change之前小时数和change之后小时数据相同，不重新渲染数据

      if (
        this.pickers[4] &&
        this.pickers[4].length === maxMinute - minMinute + 1
      ) {
        return this.pickers[4];
      }

      return this._handleData(minMinute, maxMinute, this.locale.minute);
    },
    // 获取秒数（固定）
    _getSeconds: function _getSeconds() {
      var _ref5 = {},
        _ref5$minSecond = _ref5.minSecond,
        minSecond = _ref5$minSecond === void 0 ? 0 : _ref5$minSecond,
        _ref5$maxSecond = _ref5.maxSecond,
        maxSecond = _ref5$maxSecond === void 0 ? 59 : _ref5$maxSecond; // 有开始时间和结束时间限制才走这一套逻辑

      if (this.startDate || this.endDate) {
        var year = this.year;
        var month = this.month;
        var date = this.date;
        var hour = this.hour;
        var minute = this.minute; // eslint-disable-next-line

        if (
          year === this.currentMinYear &&
          month === this.currentMinMonth &&
          date === this.currentMinDay &&
          hour === this.currentMinHour &&
          minute === this.currentMinMinute
        ) {
          // 当前年月日时分等于最小年月日时分
          minSecond = this.currentMinSeconds ? this.currentMinSeconds : 0;
          maxSecond = 59; // eslint-disable-next-line
        } else if (
          year === this.currentMaxYear &&
          month === this.currentMaxMonth &&
          date === this.currentMaxDay &&
          hour === this.currentMaxHour &&
          minute === this.currentMaxMinute
        ) {
          // 当前年月日时分等于最小年月日时分
          minSecond = 0;
          maxSecond = this.currentMaxSeconds ? this.currentMaxSeconds : 59;
        } // eslint-disable-next-line

        if (
          this.currentMinYear === this.currentMaxYear &&
          this.currentMinMonth === this.currentMaxMonth &&
          this.currentMinDay === this.currentMaxDay &&
          this.currentMinHour === this.currentMaxHour &&
          this.currentMinMinute === this.currentMaxMinute
        ) {
          // 单独处理 当前最大年月日时分等于当前最小年月日时分
          minSecond = this.currentMinSeconds ? this.currentMinSeconds : 0;
          maxSecond = this.currentMaxSeconds ? this.currentMaxSeconds : 59;
        }
      } // 如果change之前小时数和change之后小时数据相同，不重新渲染数据

      if (
        this.pickers[5] &&
        this.pickers[5].length === maxSecond - minSecond + 1
      ) {
        return this.pickers[5];
      }

      return this._handleData(minSecond, maxSecond, this.locale.second);
    },
    // 改变之后(传递回来的是选中的index和选择的该行数据)
    _handleChange: function _handleChange(index, data, wheelIndex) {
      if (data.length > 0) {
        this.pickersIndex[wheelIndex] = index;
        var type = this.pickerWheelIndex[wheelIndex];

        if (type === "yyyy") {
          this.year = "" + data[index].code;
          return;
        }

        if (type === "MM") {
          this.month = "" + data[index].code;
          return;
        }

        if (type === "dd") {
          this.date = "" + data[index].code;
          return;
        }

        if (type === "HH") {
          this.hour = "" + data[index].code;
          return;
        }

        if (type === "mm") {
          this.minute = "" + data[index].code;
          return;
        }

        if (type === "ss") {
          this.second = "" + data[index].code;
        }
      }
    },
    // 确定选择之后
    _handleConfirm: function _handleConfirm() {
      var value = this.format
        .replace("yyyy", this.year)
        .replace("MM", this.month)
        .replace("dd", this.date)
        .replace("HH", this.hour)
        .replace("mm", this.minute)
        .replace("ss", this.second);
      value = (0, _utils.formatDateTime)(
        new _utils.PickerDate({
          date: value,
          format: this.format,
        }),
        this.format
      );
      this.$emit("confirm", value);
    },
    // 取消即关闭
    _handleCancel: function _handleCancel() {
      this.$emit("cancel");
    },
    // 自定义事件
    _handleCustomTitle: function _handleCustomTitle() {
      this.$emit("custom-title");
    },
  },
});

exports["default"] = _default2;
