/**
 * @Description: 条码缓存
 * @Author: <<EMAIL>>
 * @Date: 2021-12-29 14:22:07
 * @LastEditTime: 2021-12-29 17:39:26
 * @LastEditors: <<EMAIL>>
 */

import { getTenantId, getBasicConfig } from "@/utils";
import http from "@/request";

const BasicConfig = getBasicConfig();
const tenantId = getTenantId();

const BarcodeHelper = {
  // params: string
  async validateBarcode(params) {
    const url = `${BasicConfig}/v1/${tenantId}/mt-scan-cache/barcode/existence/verify`;
    return http.post(url, params);
  },

  // params: string[]
  async validateBarcodes(params) {
    const url = `${BasicConfig}/v1/${tenantId}/mt-scan-cache/barcode/existence/batch/verify`;
    return http.post(url, params);
  },

  // [
  //   {
  //     "attr": "string",
  //     "barcode": "string", required!
  //     "containerId": 0,
  //     "containerIdentification": "string",
  //     "docNum": "string",
  //     "identfication": "string",
  //     "lineNumber": "string",
  //     "locatorCode": 0,
  //     "locatorId": 0,
  //     "locatorName": 0,
  //     "materialCode": 0,
  //     "materialId": 0,
  //     "materialLotCode": 0,
  //     "materialLotId": 0,
  //     "materialName": 0,
  //     "operationType": "string",
  //     "primaryUomCode": 0,
  //     "primaryUomId": 0,
  //     "primaryUomName": 0,
  //     "primaryUomQty": 0,
  //     "revisionCode": "string"
  //   }
  // ]
  async saveBarcodes(params) {
    const url = `${BasicConfig}/v1/${tenantId}/mt-scan-cache/barcodes/cache/save`;
    return http.post(url, params);
  },

  // params: string[]
  async delBarcodes(params) {
    const url = `${BasicConfig}/v1/${tenantId}/mt-scan-cache/barcode/cache/del`;
    return http.post(url, params);
  },
};

export default BarcodeHelper;
