<svg xmlns="http://www.w3.org/2000/svg" width="24" height="23" viewBox="0 0 24 23">
  <defs>
    <style>
      .cls-1, .cls-2, .cls-5 {
        fill: none;
        stroke-linecap: round;
      }

      .cls-1, .cls-2 {
        stroke: #F9C876;
        stroke-linejoin: round;
      }

      .cls-1 {
        opacity: 0.62;
      }

      .cls-2 {
        opacity: 0.84;
      }

      .cls-3 {
        fill: #F9C876;
        opacity: 0.21;
      }

      .cls-4 {
        fill: #FEF3E2;
      }

      .cls-5 {
        stroke: #F7B74F;
        stroke-width: 2px;
        opacity: 0.8;
      }
    </style>
  </defs>
  <g id="组_69" data-name="组 69" transform="translate(1 0.5)">
    <path id="路径_8" data-name="路径 8" class="cls-1" d="M4451.176-7393.05h-6.116s-1.544.438-1.544,1.756v5.266" transform="translate(-4443.505 7393.05)"/>
    <path id="路径_8-2" data-name="路径 8" class="cls-1" d="M4451.271-7386.025h-6.191s-1.563-.438-1.563-1.755v-5.267" transform="translate(-4443.505 7408.025)"/>
    <path id="路径_8-3" data-name="路径 8" class="cls-1" d="M4443.516-7393.05h6.116s1.544.438,1.544,1.756v5.266" transform="translate(-4429.566 7393.05)"/>
    <path id="路径_13" data-name="路径 13" class="cls-2" d="M4443.516-7386.028h6.192s1.563-.438,1.563-1.755v-5.267" transform="translate(-4429.566 7408.028)"/>
    <rect id="矩形_6" data-name="矩形 6" class="cls-3" width="14.239" height="14.348" transform="translate(4.135 4.141)"/>
    <g id="组_65" data-name="组 65" transform="translate(6.042 5.959)">
      <path id="路径_16" data-name="路径 16" class="cls-4" d="M130.705,167.3h5.55a.367.367,0,0,1,.322.192l2.115,3.931a.368.368,0,0,1,.044.174v4.458a.9.9,0,0,1-.9.9H128.9a.9.9,0,0,1-.9-.9v-4.458a.367.367,0,0,1,.052-.188l2.338-3.93A.367.367,0,0,1,130.705,167.3Zm.208.732-2.181,3.665v4.073a.448.448,0,0,0,.448.448h8.378a.448.448,0,0,0,.448-.448v-4.082l-1.969-3.656Z" transform="translate(-128 -167.296)"/>
      <path id="路径_17" data-name="路径 17" class="cls-4" d="M135.918,439.772a.366.366,0,0,1,0-.732h2.471a.367.367,0,0,1,.327.2l1.176,2.333h1.866l.983-2.311a.366.366,0,0,1,.337-.223h2.657a.366.366,0,0,1,0,.732H143.32l-.982,2.311a.366.366,0,0,1-.337.224h-2.335a.366.366,0,0,1-.327-.2l-1.176-2.331h-2.245Z" transform="translate(-135.446 -435.239)"/>
    </g>
    <line id="直线_1" data-name="直线 1" class="cls-5" x2="22" transform="translate(0 10.953)"/>
  </g>
</svg>
