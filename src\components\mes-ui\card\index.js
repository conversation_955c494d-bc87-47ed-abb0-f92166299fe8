import { isEmpty, isArray } from "lodash";
import { tagMap } from "@/utils";
import { Checkbox, CheckboxGroup } from "@hips/vue-ui";
import "./index.styl";

export default {
  name: "cardContent",
  components: {
    [Checkbox.name]: Checkbox,
    [CheckboxGroup.name]: CheckboxGroup,
  },
  props: {
    dataContent: Object,
    fields: [Array, Function],
    rightTags: [Array, Function],
    titleProps: [Object, Function],
    primaryKey: String, // 唯一索引字段
    selectionProps: {
      type: [Object, Boolean],
      default: () => ({
        selection: [String, Boolean],
        onChange: Function,
        selectedKeys: Array,
        disabled: [Function, Boolean],
      }),
    },
    deleteIcon: Object,
    backgroundClass: [Function, String], // 背景颜色class名
    backgroundImgProps: {
      type: Object,
      default: () => ({
        img: [Function, String],
        class: [Function, String],
      }),
    },
    labelWidth: [Number, String],
    expand: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isEmpty: isEmpty,
      isExpand: true,
      tagMap: tagMap,
      selectable: false,
      selectList: [],
    };
  },
  watch: {
    selectable(to) {
      if (this.selectionProps && this.selectionProps.onChange) {
        if (this.selectionProps.selection === "single") {
          if (
            !this.selectionProps.selectedKeys.includes(
              this.dataContent[this.primaryKey]
            ) &&
            to
          ) {
            this.selectionProps.onChange([this.dataContent[this.primaryKey]]);
          } else if (
            this.selectionProps.selectedKeys.includes(
              this.dataContent[this.primaryKey]
            ) &&
            !to
          ) {
            this.selectionProps.onChange([]);
          }
        }
        if (this.selectionProps.selection === "multiple") {
          if (
            !this.selectionProps.selectedKeys.includes(
              this.dataContent[this.primaryKey]
            ) &&
            to
          ) {
            this.selectionProps.onChange(
              this.selectionProps.selectedKeys.concat(
                this.dataContent[this.primaryKey]
              )
            );
          } else if (
            this.selectionProps.selectedKeys.includes(
              this.dataContent[this.primaryKey]
            ) &&
            !to
          ) {
            this.selectionProps.onChange(
              this.selectionProps.selectedKeys.filter(
                (item) => item !== this.dataContent[this.primaryKey]
              )
            );
          }
        }
      }
    },
    "selectionProps.selectedKeys": function (to) {
      this.selectable = (to || []).includes(this.dataContent[this.primaryKey]);
    },
  },

  computed: {
    currentFields() {
      if (isArray(this.fields)) {
        return this.fields;
      }
      if (typeof this.fields === "function") {
        return this.fields(this.dataContent);
      }
      return [];
    },
    normalFields() {
      const list = (this.currentFields || []).filter((e) => {
        if (typeof e.isFooter === "boolean") {
          return !e.isFooter;
        }
        if (typeof e.isFooter === "function") {
          return !e.isFooter(this.dataContent);
        }
        return [null, undefined].includes(e.isFooter);
      });
      return list;
    },
    footerFields() {
      return (this.currentFields || []).filter((e) => {
        if (typeof e.isFooter === "boolean") {
          return e.isFooter;
        }
        if (typeof e.isFooter === "function") {
          return e.isFooter(this.dataContent);
        }
      });
    },
    backgroundClassString() {
      if (!this.$slots.input && this.backgroundClass) {
        if (typeof this.backgroundClass === "string") {
          return this.backgroundClass;
        } else if (typeof this.backgroundClass === "function") {
          return this.backgroundClass(this.dataContent);
        }
        return "pda-expand-detail-bg-blue";
      }
      if (!this.$slots.input && !this.backgroundClass) {
        return "pda-expand-detail-bg-blue";
      }
    },
    currentBackgroundImg() {
      if (this.backgroundImgProps.img) {
        if (typeof this.backgroundImgProps.img === "string") {
          return this.backgroundImgProps.img;
        } else if (typeof this.backgroundImgProps.img === "function") {
          return this.backgroundImgProps.img(this.dataContent);
        }
      }
    },
    currentBackgroundImgClass() {
      if (this.currentBackgroundImg) {
        if (
          typeof this.backgroundImgProps.class === "string" &&
          !isEmpty(this.backgroundImgProps.class)
        ) {
          return this.backgroundImgProps.class;
        } else if (typeof this.backgroundImgProps.class === "function") {
          return (
            this.backgroundImgProps.class(this.dataContent) ||
            "pda-expand-detail-icon detail-icon-right icon-size-22"
          );
        }
        return "pda-expand-detail-icon detail-icon-right icon-size-22";
      }
    },
    currentTags() {
      if (isArray(this.rightTags)) {
        return this.rightTags;
      }
      if (typeof this.rightTags === "function") {
        return this.rightTags(this.dataContent);
      }
      return [];
    },
    currentTitleProps() {
      if (typeof this.titleProps === "object") {
        return this.titleProps;
      }
      if (typeof this.titleProps === "function") {
        return this.titleProps(this.dataContent);
      }
      return {};
    },
    selectionDisabled() {
      if (typeof this.selectionProps.disabled === "boolean") {
        return this.selectionProps.disabled;
      }
      if (typeof this.selectionProps.disabled === "function") {
        return this.selectionProps.disabled(this.dataContent);
      }
      return false;
    },
  },
  methods: {
    handleExpand() {
      this.isExpand = !this.isExpand;
    },

    renderValue(item) {
      if (item.render) {
        return (
          <div class="pda-expand-detail-row-item pda-expand-detail-row-component row-item-right">
            {item.render(this.dataContent)}
          </div>
        );
      }
      return (
        <div class="pda-expand-detail-row-item row-item-right">
          {this.dataContent[item.name] || ""}
        </div>
      );
    },

    handleDelete() {
      if (this.deleteIcon.delete) {
        this.deleteIcon.delete(this.dataContent);
      }
    },

    handleClickThisCard() {
      this.$emit("click", this.dataContent);
    },
  },
  render() {
    return (
      <div
        class={`${
          this.$slots.input
            ? "pda-card-input-area pda-card no-padding"
            : "pda-card-data-area"
        } mes-ui-card`}
      >
        {this.$slots.input}
        {this.$slots["card-content"]}
        {!isEmpty(this.dataContent) && (
          <div class="pda-card-padding-8">
            <div
              class={`${this.isExpand ? "expand" : ""} ${
                this.$slots.input ? "top-border-g" : ""
              } pda-expand-group`}
            >
              {this.currentTags && this.currentTags.length > 0 && (
                <div
                  class={`pda-tag-group ${
                    this.$slots.input ? "pda-tag-group-title" : ""
                  }`}
                >
                  {this.currentTags.map((e) => (
                    <span
                      style={{ marginRight: "5px" }}
                      class={`pda-tag ${tagMap[e.colorClass]}`}
                    >
                      {e.description}
                    </span>
                  ))}
                </div>
              )}
              <div class={`pda-expand-detail ${this.backgroundClassString}`}>
                {this.currentBackgroundImg && (
                  <img
                    src={this.currentBackgroundImg}
                    class={this.currentBackgroundImgClass}
                    alt="icon"
                  />
                )}
                {!this.$slots.input && (
                  <div class="pda-expand-detail-title">
                    <div class="pda-expand-detail-title-item title-item-left mes-ui-card-title">
                      {this.selectionProps.selection && (
                        <div class="pda-expand-detail-select">
                          <hips-checkbox
                            disabled={this.selectionDisabled}
                            vModel={this.selectable}
                          />
                        </div>
                      )}
                      {this.currentTitleProps &&
                        this.currentTitleProps.lineNumberField &&
                        this.dataContent[
                          this.currentTitleProps.lineNumberField
                        ] && (
                          <span
                            class="font-line-num"
                            vOn:click={this.handleClickThisCard}
                          >
                            {
                              this.dataContent[
                                this.currentTitleProps.lineNumberField
                              ]
                            }
                          </span>
                        )}
                      {this.currentTitleProps &&
                        this.currentTitleProps.titleField &&
                        this.dataContent[this.currentTitleProps.titleField] && (
                          <span
                            class="font-bolder"
                            vOn:click={this.handleClickThisCard}
                          >
                            {
                              this.dataContent[
                                this.currentTitleProps.titleField
                              ]
                            }
                          </span>
                        )}
                      {this.dataContent[
                        this.currentTitleProps.versionField
                      ] && (
                        <span>
                          （
                          {
                            this.dataContent[
                              this.currentTitleProps.versionField
                            ]
                          }
                          ）
                        </span>
                      )}
                    </div>
                    <div class="pda-expand-detail-title-item title-item-right">
                      {this.deleteIcon &&
                        this.deleteIcon.delete &&
                        !this.selectionProps.selection && (
                          <div class="m-l-4" vOn:click={this.handleDelete}>
                            <iconImg name="icon-close" className="" />
                          </div>
                        )}
                    </div>
                  </div>
                )}
                {this.currentTitleProps &&
                  this.currentTitleProps.subTitleField &&
                  this.dataContent[this.currentTitleProps.subTitleField] && (
                    <div class="pda-expand-detail-row">
                      <div class="pda-expand-detail-row-item mes-ui-card-subTitleField">
                        {this.dataContent[this.currentTitleProps.subTitleField]}
                      </div>
                    </div>
                  )}
                <div
                  class="pda-expand-detail-content"
                  vOn:click={this.handleClickThisCard}
                >
                  {this.normalFields.map((e) => (
                    <div
                      class={`pda-expand-detail-row ${
                        e.linkFunction ? "link" : ""
                      } ${e.colSpan === 1 ? "half" : ""} ${
                        !e.expand && !!this.$slots.input ? "expand" : ""
                      }`}
                      vOn:click={() =>
                        e.linkFunction ? e.linkFunction(this.dataContent) : ""
                      }
                    >
                      <div
                        class={`pda-expand-detail-row-item row-item-left ${
                          e.linkFunction ? "link" : ""
                        }`}
                        style={{ width: `${this.labelWidth}px` }}
                      >
                        {e.label}
                      </div>
                      {e.render && this.renderValue(e)}
                      {!e.render && (
                        <div
                          class={`pda-expand-detail-row-item row-item-right  ${
                            e.linkFunction ? "link" : ""
                          }`}
                        >
                          {this.dataContent[e.name] || ""}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
                {this.footerFields.length > 0 ? (
                  this.footerFields.map((e) => (
                    <div class="pda-expand-detail-content pda-expand-detail-row-top-border">
                      <div
                        key={e.name}
                        class={`pda-expand-detail-row ${
                          !e.expand && !!this.$slots.input ? "expand" : ""
                        }`}
                      >
                        <div class="pda-expand-detail-row-item row-item-left">
                          {e.label}
                        </div>
                        {e.render && this.renderValue(e)}
                        {!e.render && (
                          <div class="pda-expand-detail-row-item row-item-right">
                            {this.dataContent[e.name]}
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div></div>
                )}
              </div>
              <div class="pda-expand-group-icon">
                {this.$slots.input && this.expand && (
                  <div
                    class="pda-icon pda-icon-arrow pda-icon-12"
                    vOn:click={this.handleExpand}
                  >
                    <iconImg name="icon-arrow" className="" />
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  },
};
