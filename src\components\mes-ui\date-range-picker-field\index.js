import { Popup, Row, Col } from "@hips/vue-ui";
import IconImg from "../icon/index.vue";
import { mapLanguageGetters } from "@/utils";
import DatetimePicker from "./hips-date-picker";
import "./index.styl";

const modelPrompt = "tarzan.pda.common.component.dataRangePickerField";

export default {
  name: "dateRangePickerField",
  components: {
    [Popup.name]: Popup,
    [DatetimePicker.name]: DatetimePicker,
    [Row.name]: Row,
    [Col.name]: Col,
    [IconImg.name]: IconImg,
  },
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    // 时间格式化字符串
    format: {
      type: String,
      default: () => {
        return "yyyy-MM-dd";
      },
    },
    // 时间输入框的placeholder
    placeholder: {
      type: Object,
      default: () => {
        return {
          timeFrom: "时间从",
          timeTo: "时间至",
        };
      },
    },
    // 当前值
    value: {
      type: Array,
      default: () => {
        return Array(2).fill(undefined);
      },
    },
    // 时间选择弹窗标题
    filterTitle: {
      type: String,
    },
    icon: {
      type: String,
      default: "date-label",
    },
    defaultPickerDate: Array, // 默认日期
    required: Boolean,
    disabled: Boolean,
    label: String,
  },
  data() {
    return {
      showPopup: false, // 是否弹窗
      activeKey: "timeFrom",
      childValue: [],
    };
  },
  watch: {
    value: {
      handler(to) {
        this.childValue = to;
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    ...mapLanguageGetters(modelPrompt),
    defaultTimeFrom() {
      if (this.childValue.length > 0) {
        return this.childValue[0];
      }
      if (this.defaultPickerDate && this.defaultPickerDate.length > 0) {
        return this.defaultPickerDate[0];
      }
      return null;
    },
    defaultTimeTo() {
      if (this.childValue.length > 1) {
        return this.childValue[1];
      }
      if (this.defaultPickerDate && this.defaultPickerDate.length > 1) {
        return this.defaultPickerDate[1];
      }
      return null;
    },
  },

  methods: {
    /**
     * 显示picker弹窗
     * <AUTHOR> - hand
     * */
    handleChangeVisible() {
      if (!this.disabled) {
        this.showPopup = !this.showPopup;
      }
    },

    handleCancelSelectDate() {
      this.childValue = JSON.parse(JSON.stringify([undefined, undefined]));
      this.$emit("change", [undefined, undefined]);
      this.showPopup = !this.showPopup;
    },

    /**
     * 处理确认选择时间
     * <AUTHOR> - hand
     * @param value 格式化后的时间
     * */
    handleConfirm() {
      this.handleChangeVisible();
    },

    handleClearInputValue() {
      this.childValue = JSON.parse(JSON.stringify([undefined, undefined]));
      this.$emit("change", [undefined, undefined]);
    },

    handleChangeActiveTab(value) {
      this.activeKey = value;
    },

    handleFormatDate(number) {
      return number.length === 1 ? `0${number}` : number;
    },
    async handleChangeDate(value) {
      const { year, month, date, hour, minute, second } = value;
      let dateValue = "";
      if (this.format.toUpperCase() === "YYYY-MM-DD") {
        dateValue = `${this.handleFormatDate(year)}-${this.handleFormatDate(
          month
        )}-${this.handleFormatDate(date)}`;
      }
      if (this.format.toUpperCase() === "YYYY-MM-DD HH:MM:SS") {
        dateValue = `${this.handleFormatDate(year)}-${this.handleFormatDate(
          month
        )}-${this.handleFormatDate(date)} ${this.handleFormatDate(
          hour
        )}:${this.handleFormatDate(minute)}:${this.handleFormatDate(second)}`;
      }
      if (this.activeKey === "timeFrom") {
        this.childValue[0] = dateValue;
      }
      if (this.activeKey === "timeTo") {
        this.childValue[1] = dateValue;
      }
      this.childValue = JSON.parse(JSON.stringify(this.childValue));
      this.$emit("change", this.childValue);
    },
  },
  render() {
    return (
      <field
        vOn:clear={this.handleClearInputValue}
        required={this.required}
        inputValue={this.childValue}
        iconProps={this.iconProps}
        rightIconOptions={this.rightIconOptions}
        label={this.label}
        class="mes-ui-date-picker-range-field"
        disabled={this.disabled}
      >
        <template slot="field">
          {this.childValue[0] || this.childValue[1] ? (
            <div
              class={`pda-input-row-select ${
                this.label ? "pda-input-row-select-right" : ""
              }`}
              vOn:click={this.handleChangeVisible}
            >
              <div class="date-range-select-date">
                {this.childValue[0] || ""}
              </div>
              <div
                style={{
                  color: "#9b9b9b",
                  fontSize: "4.3vw",
                  fontWeight: "100",
                }}
              >
                ～
              </div>
              <div class="date-range-select-date-right">
                {this.childValue[1] || ""}
              </div>
            </div>
          ) : (
            <div
              class={`pda-input-row-label ${
                this.label ? "pda-input-row-select-right" : ""
              }`}
              vOn:click={this.handleChangeVisible}
            >
              <div class="date-range-select-date">
                {this.placeholder.timeFrom}
              </div>
              <div>～</div>
              <div class="date-range-select-date-right">
                {this.placeholder.timeTo}
              </div>
            </div>
          )}
          <hips-popup
            vModel={this.showPopup}
            position="bottom"
            class="mes-ui-date-range-picker-field mes-ui-standard-select-popup"
          >
            <div class="pda-select-filter-header-container">
              <div class="pda-select-filter-header">
                <div
                  class="pda-select-filter-header-cancel"
                  vOn:click={this.handleCancelSelectDate}
                >
                  {this.commonText("button.cancel", "取消")}
                </div>
                <div class="pda-select-filter-header-title">
                  {this.commonText("label.selectDate", "选择时间")}
                </div>
                <div
                  class="pda-select-filter-header-confirm"
                  vOn:click={this.handleConfirm}
                >
                  {this.commonText("button.confirm", "确定")}
                </div>
              </div>
            </div>

            <h4 class="date-picker-select-header">
              <div class="header-line">
                <div
                  class={`date-title ${
                    this.activeKey === "timeFrom"
                      ? "date-title-actived"
                      : "date-title-un-selected"
                  }`}
                  vOn:click={() => this.handleChangeActiveTab("timeFrom")}
                >
                  <div class="date-title-header">
                    {this.placeholder.timeFrom}
                  </div>
                  <div class="date-title-bottom">
                    {this.childValue[0] ||
                      this.commonText("label.pleaseSelect", "请选择")}
                  </div>
                </div>
                <div
                  class={`date-title ${
                    this.activeKey === "timeTo"
                      ? "date-title-actived"
                      : "date-title-un-selected"
                  }`}
                  vOn:click={() => this.handleChangeActiveTab("timeTo")}
                >
                  <div class="date-title-header">{this.placeholder.timeTo}</div>
                  <div class="date-title-bottom">
                    {this.childValue[1] ||
                      this.commonText("label.pleaseSelect", "请选择")}
                  </div>
                </div>
              </div>
            </h4>
            {this.activeKey === "timeFrom" && (
              <hips-datetime-picker
                format="yyyy-MM-dd"
                key="timeFrom"
                vOn:change={this.handleChangeDate}
                default-selected-value={this.defaultTimeFrom}
              />
            )}
            {this.activeKey === "timeTo" && (
              <hips-datetime-picker
                format="yyyy-MM-dd"
                key="timeTo"
                vOn:change={this.handleChangeDate}
                default-selected-value={this.defaultTimeTo}
              />
            )}
          </hips-popup>
        </template>
      </field>
    );
  },
};
