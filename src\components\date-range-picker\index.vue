<!--
 * @Description: 时间选择范围
 * @Author: <<EMAIL>>
 * @Date: 2022-03-18 16:27:11
 * @LastEditTime: 2022-05-17 20:10:25
 * @LastEditors: <<EMAIL>>
-->
<template>
  <hips-popup
    v-model="popupShowFlag"
    position="bottom"
    class="data-range-picker"
    @click-overlay="hidePop"
  >
    <div class="popup-header">
      <div class="popup-header-cancel" @click="hidePop">
        {{ commonText("button.cancel", "取消") }}
      </div>
      <div class="popup-header-title">
        {{ commonText("label.selectDate", "选择时间") }}
      </div>
      <div class="popup-header-confirm" @click="handleConfirm">
        {{ commonText("button.confirm", "确定") }}
      </div>
    </div>
    <h4 class="data-picker-select-header">
      <div class="header-line">
        <div
          class="date-title"
          :class="
            activeKey === 'currentDateFrom'
              ? 'date-title-actived'
              : 'date-title-un-selected'
          "
          @click="handleChangeActiveTab('currentDateFrom')"
        >
          <div class="date-title-header">
            {{
              dateFromTitle || commonText("label.createDateFrom", "创建时间从")
            }}
          </div>
          <div class="date-title-bottom">
            {{ currentDateFrom || commonText("label.pleaseSelect", "请选择") }}
          </div>
        </div>
        <div
          class="date-title"
          :class="
            activeKey === 'currentDateTo'
              ? 'date-title-actived'
              : 'date-title-un-selected'
          "
          @click="handleChangeActiveTab('currentDateTo')"
        >
          <div class="date-title-header">
            {{ dateToTitle || commonText("label.createDateTo", "创建时间至") }}
          </div>
          <div class="date-title-bottom">
            {{ currentDateTo || commonText("label.pleaseSelect", "请选择") }}
          </div>
        </div>
      </div>
    </h4>
    <hips-datetime-picker
      ref="dataPicker"
      format="yyyy-MM-dd"
      :default-selected-value="defaultPickerDate"
    />
  </hips-popup>
</template>

<script>
import { Popup, DatetimePicker } from "@hips/vue-ui";
import { mapLanguageGetters } from "@/utils";
const modelPrompt = "common.component";

export default {
  name: "DateRangePicker",
  components: {
    [Popup.name]: Popup,
    [DatetimePicker.name]: DatetimePicker,
  },
  props: {
    showFlag: {
      type: Boolean,
      require: true,
      default: false,
    },
    dateFrom: {
      type: String,
      require: true,
      default: "",
    },
    dateTo: {
      type: String,
      require: true,
      default: "",
    },
    dateFromTitle: {
      type: String,
      require: false,
      default: "",
    },
    dateToTitle: {
      type: String,
      require: false,
      default: "",
    },
  },
  data() {
    return {
      timer: "", // 定时器
      popupShowFlag: this.showFlag, // 是否出现
      currentDateFrom: "",
      currentDateTo: "",
      defaultPickerDate: "",
      activeKey: "currentDateFrom",
    };
  },
  computed: {
    ...mapLanguageGetters(modelPrompt),
  },
  watch: {
    showFlag: function (newValue) {
      this.popupShowFlag = newValue;
      if (newValue) {
        this.currentDateFrom = this.dateFrom;
        this.currentDateTo = this.dateTo;
        this.activeKey = "currentDateFrom";
        this.timer = setInterval(this.setSelectedDate, 500);
      } else {
        clearInterval(this.timer);
      }
    },
    activeKey: function (newValue) {
      this.defaultPickerDate = this[newValue];
    },
  },
  methods: {
    // 获取日期组件当前选中的时间
    setSelectedDate() {
      const _dataPicker = this.$refs.dataPicker;
      if (!_dataPicker) {
        return;
      }
      let date = "";
      for (let i = 0; i < 3; i++) {
        const picker = _dataPicker.pickers[i];
        const pickerIndex = _dataPicker.pickersIndex[i];
        const _newCode =
          picker[pickerIndex].code < 10
            ? `0${picker[pickerIndex].code}`
            : picker[pickerIndex].code;
        if (date) {
          date = date + "-" + _newCode;
        } else {
          date = _newCode;
        }
      }
      if (this[this.activeKey] !== date) {
        this[this.activeKey] = date;
      }
    },

    /**
     * 关闭弹窗
     * */
    hidePop() {
      this.$emit("onClose");
    },
    handleConfirm() {
      window.picker = this.$refs.dataPicker;
      this.$emit("onConfirm", {
        dateFrom: this.currentDateFrom,
        dateTo: this.currentDateTo,
      });
    },
    // 切换选中的时间
    handleChangeActiveTab(dateType) {
      if (!this[dateType]) {
        // let date = new Date(this.currentDateFrom);
        // date = date.setDate(date.getDate() + 1);
        // date = new Date(date);
        // const str =
        //   date.getFullYear() +
        //   "-" +
        //   (date.getMonth() + 1 > 9
        //     ? date.getMonth() + 1
        //     : "0" + (date.getMonth() + 1)) +
        //   "-" +
        //   (date.getDate() > 9 ? date.getDate() : "0" + date.getDate());
        this[dateType] = this.currentDateFrom || this.currentDateTo;
      }
      this.activeKey = dateType;
    },
  },
};
</script>

<style scoped>
@import "./style/index.styl";
</style>
