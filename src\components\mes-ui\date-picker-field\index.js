import { Popup, DatetimePicker, Row, Col } from "@hips/vue-ui";
import IconImg from "../icon/index.vue";
import "./index.styl";

export default {
  name: "datePickerField",
  components: {
    [Popup.name]: Popup,
    [DatetimePicker.name]: DatetimePicker,
    [Row.name]: Row,
    [Col.name]: Col,
    [IconImg.name]: IconImg,
  },
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    // 时间格式化字符串
    format: {
      type: String,
      default: () => {
        return "yyyy-MM-dd";
      },
    },
    // 时间输入框的placeholder
    placeholder: {
      type: String,
      default: () => {
        return "请选择日期";
      },
    },
    // 当前值
    value: {
      type: String,
      default: () => {
        return undefined;
      },
    },
    // 时间选择弹窗标题
    filterTitle: {
      type: String,
      default: "请选择日期时间",
    },
    icon: {
      type: String,
      default: "datepicker",
    },
    required: <PERSON><PERSON><PERSON>,
    label: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      showPopup: false, // 是否弹窗
      childValue: this.value, // 当前值
    };
  },
  methods: {
    /**
     * 显示picker弹窗
     * <AUTHOR> - hand
     * */
    handleClickDatePickerVisible() {
      if (!this.disabled) {
        this.showPopup = !this.showPopup;
      }
    },

    handleFormatDate(number) {
      return number.length === 1 ? `0${number}` : number;
    },

    /**
     * 处理确认选择时间
     * <AUTHOR> - hand
     * @param value 格式化后的时间
     * */
    handleConfirm() {
      const { year, month, date, hour, minute, second } =
        this.$refs.dateTimePickerRef;
      let dateValue = "";
      if (this.format.toUpperCase() === "YYYY-MM-DD") {
        dateValue = `${this.handleFormatDate(year)}-${this.handleFormatDate(
          month
        )}-${this.handleFormatDate(date)}`;
      }
      if (this.format.toUpperCase() === "YYYY-MM-DD HH:MM:SS") {
        dateValue = `${this.handleFormatDate(year)}-${this.handleFormatDate(
          month
        )}-${this.handleFormatDate(date)} ${this.handleFormatDate(
          hour
        )}:${this.handleFormatDate(minute)}:${this.handleFormatDate(second)}`;
      }
      this.$emit("select-confirm", dateValue);
      this.$emit("change", dateValue);
      this.childValue = dateValue;
      this.handleClickDatePickerVisible();
    },

    handleClearInputValue() {
      this.childValue = "";
      this.$emit("change", "");
    },
  },

  render() {
    return (
      <field
        vOn:clear={this.handleClearInputValue}
        required={this.required}
        inputValue={this.childValue}
        iconProps={this.iconProps}
        rightIconOptions={this.rightIconOptions}
        label={this.label}
        class="mes-ui-date-picker-field"
        disabled={this.disabled}
      >
        <template slot="field">
          {this.value ? (
            <div
              class={`pda-input-row-select ${
                this.label ? "pda-input-row-select-right" : ""
              }`}
              vOn:click={this.handleClickDatePickerVisible}
            >
              {this.value}
            </div>
          ) : (
            <div
              class={`pda-input-row-label ${
                this.label ? "pda-input-row-select-right" : ""
              }`}
              vOn:click={this.handleClickDatePickerVisible}
            >
              {this.placeholder}
            </div>
          )}
          <hips-popup vModel={this.showPopup} position="bottom">
            <hips-datetime-picker
              ref="dateTimePickerRef"
              title={this.filterTitle}
              format={this.format}
              vOn:confirm={this.handleConfirm}
              vOn:cancel={this.handleClickDatePickerVisible}
            />
          </hips-popup>
        </template>
      </field>
    );
  },
};
