import { setMockTimeout, createMock } from "@hips/plugin-vue-mock";

import moduleA from "@/modules/module-site-change";
import moduleB from "@/modules/module-container-register";

setMockTimeout(10000);

const base = [
  {
    rurl: "/api/base/constant",
    rtype: "get",
    template: {
      success: true,
      message: "success",
      data: {
        CONST_A: "A",
        CONST_B: "B"
      }
    }
  }
];

const modules = {
  moduleA,
  moduleB
};

export default createMock(base, modules);
