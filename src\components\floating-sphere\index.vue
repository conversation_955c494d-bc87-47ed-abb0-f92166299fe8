<template>
  <div
    class="floating-sphere"
    @touchstart="onTouchStart"
    @touchmove="onTouchMove"
    @touchend="onTouchEnd"
    ref="floatWindow"
    :style="{ left: left + 'px', top: top + 'px' }"
  >
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: "FloatingSphere",
  data() {
    return {
      startX: 0,
      startY: 0,
      offsetX: 0,
      offsetY: 0,
      left: 365,
      top: 80,
      isScrolling: false,
    };
  },
  methods: {
    onTouchStart(e) {
      this.startX = e.touches[0].clientX;
      this.startY = e.touches[0].clientY;
      this.offsetX = this.left;
      this.offsetY = this.top;
      this.isScrolling = false;
    },
    onTouchMove(e) {
      const x = e.touches[0].clientX - this.startX + this.offsetX;
      const y = e.touches[0].clientY - this.startY + this.offsetY;
      const maxX = window.innerWidth - this.$refs.floatWindow.offsetWidth;
      const maxY = window.innerHeight - this.$refs.floatWindow.offsetHeight;
      this.left = x < 0 ? 0 : x > maxX ? maxX : x;
      this.top = y < 0 ? 0 : y > maxY ? maxY : y;
      if (
        Math.abs(e.touches[0].clientX - this.startX) > 5 ||
        Math.abs(e.touches[0].clientY - this.startY) > 5
      ) {
        this.isScrolling = true;
      }
    },
    onTouchEnd(e) {
      if (!this.isScrolling) {
        if (e.changedTouches[0].clientX < window.innerWidth / 2) {
          this.left = 0;
        } else {
          this.left = window.innerWidth - this.$refs.floatWindow.offsetWidth;
        }
      }
    },
  },
};
</script>

<style scoped lang="stylus">
.floating-sphere {
  z-index 999999999
  position: fixed;
  left: 0;
  top: 0;
  transform: translate3d(0, 0, 0);
  transition: left 0.3s ease-out;
  border-radius 6px
  img {
    width 40px
    height 40px
  }
}
</style>
