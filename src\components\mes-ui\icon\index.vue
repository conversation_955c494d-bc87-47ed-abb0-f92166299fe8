<template>
  <div class="mes-ui-icon">
    <img
      v-if="flag"
      :src="nameUrl"
      alt="icon"
      :class="className"
      @click="handleClick"
    />
    <span @click="handleClick" v-if="!flag">
      <hips-icon :name="name" :color="color" />
    </span>
  </div>
</template>

<script>
import { Icon } from "@hips/vue-ui";

export default {
  name: "iconImg",
  components: {
    [Icon.name]: Icon,
  },
  props: {
    name: String,
    type: {
      type: String, // icon、img
      default: "img",
    },
    imageType: {
      type: String,
      default: "svg",
    },
    click: Function,
    className: {
      type: String,
      default: "label-icon",
    },
    color: {
      type: String,
      default: "#338ce8",
    },
  },

  computed: {
    flag: function () {
      let flag = false;
      try {
        flag = this.type === "img";
      } catch (e) {
        flag = this.type !== "img";
      }
      return flag;
    },
    nameUrl: function () {
      let url = null;
      if (this.type === "img") {
        try {
          const srcUrl = require(`@/assets/img/${this.name}.${this.imageType}`);
          if (srcUrl) {
            url = srcUrl;
          }
        } catch (e) {
          url = require(`@/assets/img/${this.name.slice(
            0,
            this.name.length - 2
          )}.${this.imageType}`);
        }
      }
      return url;
    },
  },
  methods: {
    handleClick() {
      this.$emit("click");
    },
  },
};
</script>

<style scoped lang="stylus">
@import './index.styl';
</style>
