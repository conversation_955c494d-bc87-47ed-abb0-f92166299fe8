<!--
 * @Description: 
 * @Author: <<EMAIL>>
 * @Date: 2022-02-28 14:05:24
 * @LastEditTime: 2022-04-29 10:37:27
 * @LastEditors: <<EMAIL>>
-->
<script>
import { Popup, Input, Spin } from "@hips/vue-ui";

export default {
  name: "Select",
  components: {
    [Popup.name]: Popup,
    [Input.name]: Input,
    [Spin.name]: Spin,
  },
  props: {
    show: {
      type: Boolean,
    },
    loading: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    confirm: {
      type: Function,
      default: () => {},
    },
    cancel: {
      type: Function,
      default: () => {},
    },
    maxNum: {
      type: String,
      default: () => {
        return "";
      },
    },
    uomCode: {
      type: String,
      default: () => {
        return "";
      },
    },
    inputTextLabel: {
      type: String,
      default: () => {
        return "";
      },
    },
    noteTextLabel: {
      type: String,
      default: () => {
        return "";
      },
    },
    noteTextRuleStart: {
      type: String,
      default: () => {
        return "";
      },
    },
    noteTextRuleEnd: {
      type: String,
      default: () => {
        return "";
      },
    },
    language: {
      type: Object,
      default: () => {
        return {};
      },
    },
    barcodeInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      splitNum: "", // 拆分数量
      surplusNum: "", // 剩余数量
      splitActive: true,
    };
  },
  watch: {
    maxNum() {
      this.surplusNum = ``;
    },
    splitNum(to) {
      console.log(to);
      this.surplusNum = this.subtr(this.maxNum, to);
    },
    show(to) {
      this.splitNum = "";
      if (to) {
        document.addEventListener("keyup", this.keyupListener);
      } else {
        document.removeEventListener("keyup", this.keyupListener);
      }
    },
  },
  mounted() {},
  destroyed() {
    document.removeEventListener("keyup", this.keyupListener);
  },
  methods: {
    subtr(arg1, arg2) {
      if (arg1 === "-1") {
        return arg2;
      }
      let r1, r2, m, n;
      try {
        r1 = arg1.toString().split(".")[1].length;
      } catch (e) {
        r1 = 0;
      }
      try {
        r2 = arg2.toString().split(".")[1].length;
      } catch (e) {
        r2 = 0;
      }
      m = Math.pow(10, Math.max(r1, r2));
      //last modify by deeka
      //动态控制精度长度
      n = r1 >= r2 ? r1 : r2;
      return ((arg1 * m - arg2 * m) / m).toFixed(n);
    },
    commonText(code, defaultText) {
      return this.language[`tarzan.pda.common.${code}`] || defaultText;
    },
    focusInput() {
      this.$refs?.inputRef?.$refs?.input?.focus();
      this.$refs?.inputRef?.$refs?.input?.select();
      this.splitActive = false;
    },
    keyupListener(e) {
      const { keyCode, key } = e;
      if (keyCode === 8 || key === "Backspace") {
        this.pageDelete();
      }
      if ((key >= 0 && key <= 9) || key === ".") {
        this.pagePress(key);
      }
    },
    pageCancel() {
      if (this.loading) {
        return;
      }
      this.cancel();
      this.splitNum = "";
    },
    pageConfirm() {
      if (this.splitNum && this.splitNum !== 0 && this.splitNum !== "0") {
        this.confirm({
          splitNum: this.splitNum,
        });
      }
    },
    pagePress(num) {
      if (this.splitActive) {
        let splitNum = this.splitNum;
        if (splitNum.length === 0) {
          if (num === ".") {
            splitNum = "0.";
          } else {
            splitNum = `${num}`;
          }
        } else if (splitNum === "0") {
          if (`${num}` !== "0") {
            splitNum = `${splitNum}${num}`;
          }
        } else if (`${num}` === ".") {
          if (splitNum.indexOf(".") === -1) {
            splitNum = `${splitNum}${num}`;
          }
        } else {
          splitNum = `${splitNum}${num}`;
        }
        if (splitNum.length > 17) {
          return;
        }
        if (this.maxNum === "-1") {
          // this.splitNum = splitNum.indexOf("." === -1)
          //   ? splitNum
          //   : `${splitNum - 0}`;
          if (splitNum.indexOf(".") !== -1) {
            if (
              splitNum &&
              splitNum.split(".").pop().length > 0 &&
              splitNum.split(".").pop().length ===
                Number(this.barcodeInfo.decimalNumber) + 1
            ) {
              this.splitNum = parseFloat(
                String(
                  Number(splitNum).toFixed(
                    Number(this.barcodeInfo.decimalNumber)
                  )
                )
              );
            } else {
              this.splitNum = splitNum;
            }
          } else {
            this.splitNum = splitNum;
          }
        } else if (this.maxNum - splitNum >= 0) {
          if (splitNum.indexOf(".") !== -1) {
            if (
              splitNum &&
              splitNum.split(".").pop().length > 0 &&
              splitNum.split(".").pop().length ===
                Number(this.barcodeInfo.decimalNumber) + 1
            ) {
              this.splitNum = parseFloat(
                String(
                  Number(splitNum).toFixed(
                    Number(this.barcodeInfo.decimalNumber)
                  )
                )
              );
            } else {
              this.splitNum = splitNum;
            }
          } else {
            this.splitNum = splitNum;
          }
        } else {
          this.splitNum = this.maxNum;
        }
      }
    },
    pageDelete() {
      if (this.splitActive) {
        const splitNum = String(this.splitNum).split("");
        if (splitNum.length > 0) {
          splitNum.pop();
        }
        this.splitNum = splitNum.join("");
      }
    },
    pageDeleteAll() {
      if (this.splitActive) {
        this.splitNum = "";
      }
    },
  },
};
</script>

<template>
  <div>
    <hips-popup
      v-model="show"
      position="bottom"
      :overlay-on-click-close="false"
      :style="{ padding: 0, height: 'auto' }"
      @click-overlay="pageCancel"
    >
      <div class="split-container">
        <div v-if="loading" class="split-container-spin">
          <hips-spin size="20px" />
        </div>
        <div v-if="noteTextLabel" class="pda-split-row pda-split-row-note">
          <div class="pda-split-row-note-text">
            ({{ commonText("model.tips", "提示") }}:{{ noteTextLabel
            }}<span v-show="maxNum !== '-1'" class="note-text-yellow"
              >{{ noteTextRuleStart }}{{ maxNum }}{{ noteTextRuleEnd }}</span
            >)
          </div>
          <div v-if="uomCode && uomCode !== ''" class="pda-split-row-note-uom">
            {{ commonText("uomCode", "单位") }}:{{ uomCode }}
          </div>
        </div>
        <div class="pda-split-row pda-split-row-half">
          <div class="pad-split-row-col">
            <template v-if="inputTextLabel || noteTextLabel">
              {{ inputTextLabel || noteTextLabel }}
            </template>
            <template v-else>
              {{ commonText("quantity", "数量") }}
            </template>
          </div>
          <div
            class="pad-split-row-col"
            :class="{ 'split-active': splitActive }"
            @click="splitActive = true"
          >
            <div class="key-board-value">
              {{ splitNum }}
            </div>
          </div>
        </div>
        <hips-number-keyboard
          v-model="show"
          :class="{
            'keyboard-disabled':
              !splitNum || splitNum === 0 || splitNum === '0',
          }"
          :complete-text="
            language['tarzan.pda.common.button.confirm'] || '确定'
          "
          @press="pagePress"
          @delete="pageDelete"
          @completed="pageConfirm"
          @delete-all="pageDeleteAll"
        />
      </div>
    </hips-popup>
  </div>
</template>

<style scoped lang="stylus">
@keyframes inputAnimation {
  0% { opacity: 1; }
  49% { opacity: 1; }
  50% { opacity: 0; }
  99% { opacity: 0; }
  100% { opacity: 1; }
}
.pda-split-row{
  padding: 0 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  .pad-split-row-col{
    flex-grow: 1;
    line-height: 50px;
    height: 50px;
    border-bottom: 1px solid #EEEEEE;
    font-size: 16px;
    color: #3C4363;
    &.pad-split-row-col-surplus{
      color: #9B9B9B
    }
  }
  .pad-split-row-input{
    flex-grow: 1;
  }
}
.split-active{
  position: relative
}
.split-active:after{
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  height: 16px;
  margin: auto;
  width: 1px;
  background-color: #458BF7;
  z-index: 1;
  animation:inputAnimation 0.9s infinite;
}
.pda-split-row-note{
  padding: 12px 25px 4px;
  font-size: 12px;
  line-height: 14px;
  color: #9B9B9B;
  display: flex;
  align-items: flex-start;
  .pda-split-row-note-text{
    flex-grow: 1;
    flex-shrink: 1;
  }
  .pda-split-row-note-uom{
    flex-grow: 0;
    flex-shrink: 0;
  }
  .note-text-yellow{
    color: #F5A623;
  }
}
.pda-split-row-half{
  .pad-split-row-col{
    width: 50%;
  }
}
.key-board-value{
  text-align: right;
}


// 虚拟输入框相关
.hips-number-keyboard{
  padding: 4px 4px 4px;
  position: relative;
  background-color: #F3F4F9;
  >>> .hips-number-keyboard__body{
    width: 75%;
  }
  >>> .hips-row >.hips-col:nth-child(10){
    display: none;
  }
  >>> .hips-row >.hips-col:nth-child(11){
    width: 66.66%;
    padding-bottom: 0
  }
  >>> .hips-row >.hips-col:nth-child(12){
    padding-bottom: 0
  }
  >>> .hips-number-keyboard__right-side {
    padding: 4px 4px 0;
    width: 25%;
  }
  >>> .hips-col {
    padding: 4px;
  }
  >>> .hips-keyboard-key{
    height: 48px;
    line-height: 48px;
    font-size: 25px;
    background-color: #FFFFFF;
    border-radius: 6px;
    font-family: PingFangSC-Regular;
  }
  >>> .hips-keyboard-key--active{
    height: 48px;
    line-height: 48px;
    font-size: 25px;
    background-color: #c9c9c9;
  }
  >>> .hips-number-keyboard__delete{
    height: 48px;
    margin-bottom: 8px;
    border-radius: 6px;
  }
  >>> .hips-number-keyboard__complete{
    height: 160px;
    border-radius: 6px;
  }
  &.keyboard-disabled {
    >>> .hips-number-keyboard__complete{
      background-color: #9FCBF1;
    }
  }
}
.split-container{
  position: relative;
  .split-container-spin{
    z-index: 1001;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
