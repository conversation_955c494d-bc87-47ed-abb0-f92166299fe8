import { Input, Icon } from "@hips/vue-ui";
import { Store } from "@hips/vue-core";
import "./index.styl";

import { confirmExit, mapLanguageGetters } from "@/utils";

const { mapState } = Store;
const modelPrompt = "tarzan.pda.common.component.headerFilterField";

export default {
  name: "headerFilterField",
  components: {
    [Input.name]: Input,
    [Icon.name]: Icon,
  },
  model: {
    prop: "fatherValue",
    event: "change",
  },
  props: {
    clear: Function,
    placeholder: String,
    enter: Function,
    fatherValue: String,
  },
  data() {
    return {
      childValue: "",
    };
  },
  watch: {
    fatherValue: {
      handler: function (to) {
        this.childValue = to;
      },
      immediate: true,
      deep: true,
    },
    childValue: {
      handler: function (to) {
        this.$emit("change", to);
      },
      immediate: true,
      deep: true,
    },
  },

  computed: {
    ...mapState("loadingStore", {
      globalLoading: (state) => state.global,
    }),
    ...mapLanguageGetters(modelPrompt),
  },

  methods: {
    exit() {
      confirmExit();
    },

    focusInput() {
      this.$refs.inputRef.$refs.input.focus();
      this.$refs.inputRef.$refs.input.select();
    },

    // 查询框
    handleEnter() {
      this.$emit("enter", this.childValue);
    },

    // 选择按钮逻辑
    handleClearInputValue() {
      this.childValue = "";
      this.$emit("change", "");
      this.$emit("clear", "");
    },
  },
  render() {
    return (
      <div slot="center" class="pda-header-search">
        <div
          class="pda-header-search-item pda-header-search-item-img"
          vOn:click={this.focusInput}
        >
          <div class="pda-icon">
            <iconImg name="add-container" />
          </div>
        </div>
        <div class="pda-header-search-item">
          <hips-input
            ref="inputRef"
            vModel={this.childValue}
            placeholder={this.placeholder}
            type="text"
            vOn:enter={this.handleEnter}
          />
        </div>
        {this.childValue && (
          <div
            class="pda-header-search-item pda-header-search-item-img"
            vOn:click={() => this.handleClearInputValue()}
          >
            <hips-icon name="clear" color="#CDCDCD" size="20" />
          </div>
        )}
      </div>
    );
  },
};
