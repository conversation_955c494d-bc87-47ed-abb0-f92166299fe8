@import "../../../style/pda-var.styl"

.pda-header-search{
  padding: 0 4px;
  position: relative;
  top: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  background-color: $color-header-search-bg;
  border: 1px solid $color-header-search-border;
  border-radius: 4px;
  .pda-header-search-item{
    flex-grow: 1;
    font-size: 16px;
    .hips-input{
      padding-left: 10px;
      padding-right: 10px;
      height: 28px;
      background-color: $color-header-search-bg;
    }
    .hips-input:after{
      border: none !important}
    .hips-input__input-value{
      font-size: 14px;
      height: 28px;
      input{
        background-color: $color-header-search-bg;
      }

    }
    &.pda-header-search-item-img{
      display: flex;
      justify-content: center;
      align-items: center;
      width: 20px;
      height: 20px;
      flex-grow: 0;
    }
  }
}
.pda-header-search-item{
  font-size: 16px;
}