.mes-ui-field.pda-input-row {
  display: flex;
  flex-direction: row;
  background-color: $color-card-bg
  border-radius: 4px;
  flex-shrink: 0;
  flex-grow: 0;
  overflow: hidden;
  padding-left: 10px;
  padding-right: 10px;
  .pda-input-row-input-label {
    width: 30%;
    .pda-input-row-label {
      padding-left: 0 !important;
      flex-grow: 0 !important;
      font-weight: 400 !important;
      padding-left: 3.2vw;
      padding-right: 3.2vw;
      justify-content: flex-start !important;
    }
    .label-required:after {
      left: -5%
    }
  }
  .pda-input-row-icon{
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .pda-input-row-icon:first-of-type{
  }
  .pda-input-row-icon:last-of-type{
  }
  .pda-input-row-input-right-algin {
    display: flex;
    text-align: end !important;
    justify-content: flex-end;
    .hips-input__input-value {
      input {
        text-align: end !important;
      }
    }
    .pda-input-row-label {
      justify-content: flex-end;
    }
  }
  .pda-input-row-input{
    flex-grow: 1;
    .hips-input{
      padding-left: 10px;
      padding-right: 10px;
      height: 36px;
      
    }
    .hips-input:after{
      border: none !important
    }
  }
  .pda-input-row-label{
    padding-left: 12px;
    flex-grow: 1
    height: 40px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: $color-input-placeholder;
    font-size: 16px;
    font-weight: 100;
  }
  .pda-input-row-select{
    padding-left: 12px;
    flex-grow: 1
    height: 40px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: $color-input;
    font-size: 14px;
    font-weight: 600
  }
  .pda-input-row-select.pda-input-row-input-right-algin {
    justify-content: flex-end;
    padding-right: 12px;
  }
  .pda-input-row-input-right-algin {
    .pda-input-row-select {
      justify-content: flex-end;
      padding-right: 12px;
    }
  }
  .pda-input-row-date-range-select{
    padding: 0 8px;
    text-align: center;
    flex-grow: 1
    height: 40px;
    line-height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: $color-input;
    font-size: 14px;
    font-weight: 600
    .date-range-select-date{
      flex: 1;
    }
  }
  .pda-input-row-icon-front{
    padding-top: 2px;
    font-size: 20px;
    color: $color-input-placeholder;
  }

  .label-required-left:after,
  .label-required:after {
    position: absolute;
    left: 110%;
    top: 3px;
    bottom: 0;
    content: "*";
    color: #f96f68;
    padding-right: 0.533vw;
    font-size:10px;
    display: flex;
    align-items: center;
  }
  .label-required-left:after {
    left: 10%;
  }

  .label-required-left,
  .label-required{
    position relative;
    z-index: 1;
  }

}


.mes-ui-field.pda-input-row-top-border {
  border-top: 1px solid #eee;
}
.mes-ui-card {
  .row-item-right {
    padding-right: 10px;
    .max-height-2row .mes-ui-field.pda-input-row .pda-input-row-label,
    .max-height-2row .mes-ui-field.pda-input-row .pda-input-row-select {
      justify-content: flex-end;
      margin-right: 10px;
    }
    .max-height-2row .mes-ui-field.pda-input-row .pda-input-row-label {
      color: #9B9B9B
      font-weight: 100;
      font-size: 12px !important;
      margin-right: 10px;
    }
    .max-height-2row.mes-ui-card-input  {
      input {
        text-align: right !important;
        font-size: 13px !important;
        font-weight: 400 !important;
      }
      .hips-input__body .hips-input__input-value input {
        color: #3c4363;
        font-weight:600;
      }
      .hips-input {
        input::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
          color: #9B9B9B
          font-weight: 100;
          font-size: 12px !important;
        }

        input:-moz-placeholder, textarea:-moz-placeholder {
          color: #9B9B9B
          font-weight: 100;
          font-size: 12px !important;
        }

        input::-moz-placeholder, textarea::-moz-placeholder {
          color: #9B9B9B
          font-weight: 100;
          font-size: 12px !important;
        }

        input:-ms-input-placeholder, textarea:-ms-input-placeholder {
          color: #9B9B9B
          font-weight: 100;
          font-size: 12px !important;
        }
      }
    }
  }

  .max-height-2row  {
    .mes-ui-field.pda-input-row .pda-input-row-label,
    .mes-ui-field.pda-input-row .pda-input-row-select {
      height: auto !important;
    }
    .mes-ui-field.pda-input-row {
      padding-right: 0;
    }
    .mes-ui-field.pda-input-row .pda-input-row-icon {
      height: auto;
    }
  }

  .pda-expand-detail-row-top-border {
    padding-top: 2px;
    margin-top: 2px;
    .pda-expand-detail-row {
      align-items: center;
    }
  }
}
