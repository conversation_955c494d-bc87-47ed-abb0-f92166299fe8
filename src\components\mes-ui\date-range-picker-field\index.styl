.mes-ui-date-range-picker-field{
  border-radius: 4px 4px 0 0;
  .hips-picker__toolbar {
    display: none;
  }
  .popup-header{
    width: 100%;
    height: 52px;
    font-size: 16px;
    text-align: center;
    display: inline-flex;
    justify-content: space-between;
    align-items: center;
  }
  .popup-header-cancel{
    padding: 20px;
    color: #4D4D4D;
  }

  .popup-header-title{
    color: #6D7A80;
    flex: 1;
  }

  .popup-header-confirm{
    padding: 20px;
    color: #1f8ceb;
  }

  .date-picker-select-header{
    height: 58px;
    font-size: 16px;
    color: #666;
    text-align: center;
    border-top-width: 1px;
    border-style: solid;
    border-color: #EEEEEE;

  }
  .date-picker-select-header .header-line{
      width: 100%;
      height: 57px;
      display: inline-flex;
  }

  .date-title {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .date-title-actived{
    color: #1f8ceb;
  }

  .date-title-un-selected {
    background: #00000008;
    border-bottom-width: 1px;
    border-style: solid;
    border-color: #EEEEEE;
  }

  .date-title-bottom{
    margin-top: 4px;
    font-size: 14px;  
  }

  .date-range-select-date{
    width: 35%;
  }

  .date-range-select-date-right {
    width: 40%;
    text-align: center;
  }
}