import http from "@/requestNew";
import { getTenantId } from "@/utils";

const tenantId = getTenantId();
const BASE_SERVER = `/re-mes`;

/**
 * @description 设备登录
 * @param {*} params
 * @returns {*}
 */
export async function loginApi(params) {
  const url = `${BASE_SERVER}/v1/${tenantId}/hme-cell-packing/wkc/login`;
  return http.post(url, params);
}

/**
 * @description 包材扫描
 * @param {*} params
 * @returns {*}
 */
export async function materialScanApi(params) {
  const url = `${BASE_SERVER}/v1/${tenantId}/hme-cell-packing/pack/material/scan`;
  return http.post(url, params);
}

/**
 * @description 箱码扫描
 * @param {*} params
 * @returns {*}
 */
export async function packScanApi(params) {
  const url = `${BASE_SERVER}/v1/${tenantId}/hme-cell-packing/pack/code/scan`;
  return http.post(url, params);
}

/**
 * @description 电芯码扫描
 * @param {*} params
 * @returns {*}
 */
export async function cellScanApi(params) {
  sessionStorage.setItem("fliterToastUrl", "hme-cell-packing/cell/code/scan");
  const url = `${BASE_SERVER}/v1/${tenantId}/hme-cell-packing/cell/code/scan`;
  return http.post(url, params);
}

/**
 * @description 电芯装载
 * @param {*} params
 * @returns {*}
 */
export async function cellLoadApi(params) {
  const url = `${BASE_SERVER}/v1/${tenantId}/hme-cell-packing/cell/load`;
  return http.post(url, params);
}

/**
 * @description 电芯卸载
 * @param {*} params
 * @returns {*}
 */
export async function cellUnloadApi(params) {
  const url = `${BASE_SERVER}/v1/${tenantId}/hme-cell-packing/cell/unload`;
  return http.post(url, params);
}

/**
 * @description 电芯批量卸载
 * @param {*} params
 * @returns {*}
 */
export async function cellBatchUnloadApi(params) {
  const url = `${BASE_SERVER}/v1/${tenantId}/hme-cell-packing/cell/batch/unload`;
  return http.post(url, params);
}

/**
 * @description 获取打印数据
 * @param {*} params
 * @returns {*}
 */
export async function getPrintDataApi(params) {
  const url = `${BASE_SERVER}/v1/${tenantId}/hme-cell-packing/cell/print/query`;
  return http.get(url, params);
}

/**
 * @description 获取工作订单列表
 * @param {*} params
 * @returns {*}
 */
export async function getWorkOrderListApi(params) {
  const url = `${BASE_SERVER}/v1/${tenantId}/hme-cell-packing/wo/choose/list`;
  return http.post(url, params);
}
